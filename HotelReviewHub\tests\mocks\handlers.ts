import { http, HttpResponse } from 'msw';

// Mock API handlers for testing
export const handlers = [
  // Business info endpoint
  http.get('/api/business', () => {
    return HttpResponse.json({
      businessInfo: {
        id: 1,
        name: 'Test Hotel',
        address: '123 Test Street',
        accountId: 'test-account-id',
        locationId: 'test-location-id',
        refreshToken: null,
        lastSync: null
      }
    });
  }),

  // Reviews endpoint
  http.get('/api/reviews', () => {
    return HttpResponse.json([
      {
        id: 1,
        googleId: 'test-review-1',
        rating: 5,
        comment: 'Great hotel!',
        reviewerName: '<PERSON>',
        reviewDate: '2024-01-01T00:00:00Z',
        responseComment: null,
        responseDate: null,
        sentiment: 'positive',
        fetchedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        googleId: 'test-review-2',
        rating: 3,
        comment: 'Average experience',
        reviewerName: '<PERSON>',
        reviewDate: '2024-01-02T00:00:00Z',
        responseComment: null,
        responseDate: null,
        sentiment: 'neutral',
        fetchedAt: '2024-01-02T00:00:00Z'
      }
    ]);
  }),

  // Review stats endpoint
  http.get('/api/reviews/stats', () => {
    return HttpResponse.json({
      totalReviews: 2,
      averageRating: 4.0,
      ratingDistribution: {
        1: 0,
        2: 0,
        3: 1,
        4: 0,
        5: 1
      },
      sentimentDistribution: {
        positive: 1,
        neutral: 1,
        negative: 0
      }
    });
  }),

  // Setup endpoint
  http.get('/api/setup', () => {
    return HttpResponse.json({
      isSetup: false,
      setupUrl: 'https://accounts.google.com/oauth/authorize?test=true'
    });
  }),

  // Quota status endpoint
  http.get('/api/quota-status', () => {
    return HttpResponse.json({
      hasBusinessInfo: true,
      businessName: 'Test Hotel',
      connected: true,
      quotaIssue: false
    });
  }),

  // Sync endpoint
  http.post('/api/sync', () => {
    return HttpResponse.json({
      success: true,
      message: 'Sync completed successfully',
      reviewsFound: 2,
      newReviews: 0
    });
  }),

  // Review response endpoint
  http.post('/api/reviews/:id/response', () => {
    return HttpResponse.json({
      id: 1,
      googleId: 'test-review-1',
      rating: 5,
      comment: 'Great hotel!',
      reviewerName: 'John Doe',
      reviewDate: '2024-01-01T00:00:00Z',
      responseComment: 'Thank you for your feedback!',
      responseDate: '2024-01-03T00:00:00Z',
      sentiment: 'positive',
      fetchedAt: '2024-01-01T00:00:00Z'
    });
  }),

  // Logout endpoint
  http.post('/api/logout', () => {
    return HttpResponse.json({
      success: true,
      message: 'Successfully logged out'
    });
  }),

  // Error handler for unhandled requests
  http.all('*', ({ request }) => {
    console.warn(`Unhandled ${request.method} request to ${request.url}`);
    return new HttpResponse(null, { status: 404 });
  })
];
