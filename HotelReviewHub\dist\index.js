var __defProp = Object.defineProperty;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// shared/schema.ts
import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { sql } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
var users, businessInfo, reviews, insertUserSchema, insertBusinessInfoSchema, insertReviewSchema, updateReviewResponseSchema;
var init_schema = __esm({
  "shared/schema.ts"() {
    "use strict";
    users = sqliteTable("users", {
      id: integer("id").primaryKey({ autoIncrement: true }),
      username: text("username").notNull().unique(),
      email: text("email").notNull().unique(),
      name: text("name").notNull(),
      password: text("password").notNull(),
      createdAt: text("created_at").notNull().default(sql`CURRENT_TIMESTAMP`)
    });
    businessInfo = sqliteTable("business_info", {
      id: integer("id").primaryKey({ autoIncrement: true }),
      name: text("name").notNull(),
      address: text("address"),
      accountId: text("account_id").notNull(),
      locationId: text("location_id").notNull(),
      refreshToken: text("refresh_token"),
      lastSync: text("last_sync")
      // SQLite doesn't have native timestamp, using text
    });
    reviews = sqliteTable("reviews", {
      id: integer("id").primaryKey({ autoIncrement: true }),
      googleId: text("google_id").notNull().unique(),
      rating: integer("rating").notNull(),
      comment: text("comment").default(""),
      reviewerName: text("reviewer_name").notNull(),
      reviewDate: text("review_date").notNull(),
      // SQLite doesn't have native timestamp, using text
      responseComment: text("response_comment"),
      responseDate: text("response_date"),
      // SQLite doesn't have native timestamp, using text
      sentiment: text("sentiment").notNull(),
      // positive, negative, neutral
      fetchedAt: text("fetched_at").notNull()
      // SQLite doesn't have native timestamp, using text
    });
    insertUserSchema = createInsertSchema(users).pick({
      username: true,
      email: true,
      name: true,
      password: true
    });
    insertBusinessInfoSchema = createInsertSchema(businessInfo).omit({
      id: true
    });
    insertReviewSchema = createInsertSchema(reviews).omit({
      id: true
    });
    updateReviewResponseSchema = z.object({
      responseComment: z.string().min(1)
    });
  }
});

// server/utils/encryption.ts
import crypto from "crypto";
function getEncryptionKey() {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) {
    throw new Error("ENCRYPTION_KEY environment variable is not set");
  }
  if (key.length < 32) {
    throw new Error("ENCRYPTION_KEY must be at least 32 characters long");
  }
  return crypto.createHash("sha256").update(key).digest();
}
function encrypt(plaintext) {
  if (!plaintext) {
    return "";
  }
  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipherGCM(ALGORITHM, key, iv);
    cipher.setAAD(Buffer.from("HotelReviewHub-v1"));
    let encrypted = cipher.update(plaintext, "utf8");
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    const tag = cipher.getAuthTag();
    const combined = Buffer.concat([iv, encrypted, tag]);
    return combined.toString("base64");
  } catch (error) {
    console.error("\u274C Encryption error:", error);
    throw new Error("Failed to encrypt data");
  }
}
function decrypt(encryptedData) {
  if (!encryptedData) {
    return "";
  }
  try {
    const key = getEncryptionKey();
    const combined = Buffer.from(encryptedData, "base64");
    const iv = combined.subarray(0, IV_LENGTH);
    const tag = combined.subarray(combined.length - TAG_LENGTH);
    const encrypted = combined.subarray(IV_LENGTH, combined.length - TAG_LENGTH);
    const decipher = crypto.createDecipherGCM(ALGORITHM, key, iv);
    decipher.setAAD(Buffer.from("HotelReviewHub-v1"));
    decipher.setAuthTag(tag);
    let decrypted = decipher.update(encrypted, void 0, "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  } catch (error) {
    console.error("\u274C Decryption error:", error);
    return "";
  }
}
function isEncrypted(data) {
  if (!data) {
    return false;
  }
  try {
    const decoded = Buffer.from(data, "base64");
    const minLength = IV_LENGTH + TAG_LENGTH + 1;
    return decoded.length >= minLength && data === decoded.toString("base64");
  } catch {
    return false;
  }
}
function safeEncrypt(data) {
  if (data === null || data === void 0) {
    return null;
  }
  if (data === "") {
    return "";
  }
  return encrypt(data);
}
function safeDecrypt(data) {
  if (data === null || data === void 0) {
    return null;
  }
  if (data === "") {
    return "";
  }
  if (isEncrypted(data)) {
    const decrypted = decrypt(data);
    return decrypted || data;
  }
  return data;
}
function validateEncryptionConfig() {
  try {
    getEncryptionKey();
    return true;
  } catch {
    return false;
  }
}
var ALGORITHM, IV_LENGTH, TAG_LENGTH;
var init_encryption = __esm({
  "server/utils/encryption.ts"() {
    "use strict";
    ALGORITHM = "aes-256-gcm";
    IV_LENGTH = 16;
    TAG_LENGTH = 16;
  }
});

// server/storage.ts
var storage_exports = {};
__export(storage_exports, {
  MemStorage: () => MemStorage,
  SQLiteStorage: () => SQLiteStorage,
  storage: () => storage
});
import { drizzle } from "drizzle-orm/better-sqlite3";
import { eq, desc, and } from "drizzle-orm";
import Database from "better-sqlite3";
var SQLiteStorage, MemStorage, storage;
var init_storage = __esm({
  "server/storage.ts"() {
    "use strict";
    init_schema();
    init_encryption();
    SQLiteStorage = class {
      db;
      sqlite;
      constructor(databasePath = "./database.sqlite") {
        try {
          this.sqlite = new Database(databasePath);
          this.sqlite.pragma("journal_mode = WAL");
          this.sqlite.pragma("foreign_keys = ON");
          this.db = drizzle(this.sqlite);
          console.log(`\u2705 SQLite database initialized at ${databasePath}`);
          if (!validateEncryptionConfig()) {
            console.warn("\u26A0\uFE0F Encryption configuration invalid - sensitive data will not be encrypted");
          } else {
            console.log("\u{1F512} Database encryption enabled");
          }
          this.testConnection();
        } catch (error) {
          console.error("\u274C Failed to initialize SQLite database:", error);
          throw error;
        }
      }
      testConnection() {
        try {
          this.sqlite.prepare("SELECT 1").get();
          console.log("\u2705 Database connection test successful");
        } catch (error) {
          console.error("\u274C Database connection test failed:", error);
          throw error;
        }
      }
      async getUser(id) {
        try {
          const result = await this.db.select().from(users).where(eq(users.id, id)).limit(1);
          return result[0];
        } catch (error) {
          console.error("\u274C Error getting user:", error);
          throw new Error("Failed to retrieve user");
        }
      }
      async getUserById(id) {
        return this.getUser(id);
      }
      async getUserByUsername(username) {
        try {
          const result = await this.db.select().from(users).where(eq(users.username, username)).limit(1);
          return result[0];
        } catch (error) {
          console.error("\u274C Error getting user by username:", error);
          throw new Error("Failed to retrieve user");
        }
      }
      async getUserByEmail(email) {
        try {
          const result = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
          return result[0];
        } catch (error) {
          console.error("\u274C Error getting user by email:", error);
          throw new Error("Failed to retrieve user");
        }
      }
      async createUser(insertUser) {
        try {
          const result = await this.db.insert(users).values(insertUser).returning();
          console.log("\u2705 User created successfully");
          return result[0].id;
        } catch (error) {
          console.error("\u274C Error creating user:", error);
          if (error.message?.includes("UNIQUE constraint failed")) {
            throw new Error("Username already exists");
          }
          throw new Error("Failed to create user");
        }
      }
      async getBusinessInfo() {
        try {
          const result = await this.db.select().from(businessInfo).limit(1);
          if (result[0]) {
            return {
              ...result[0],
              refreshToken: safeDecrypt(result[0].refreshToken)
            };
          }
          return result[0];
        } catch (error) {
          console.error("\u274C Error getting business info:", error);
          throw new Error("Failed to retrieve business information");
        }
      }
      async createOrUpdateBusinessInfo(info) {
        try {
          if (!info.name || !info.accountId && info.accountId !== "disconnected" || !info.locationId && info.locationId !== "disconnected") {
            throw new Error("Missing required business information fields");
          }
          const existing = await this.getBusinessInfo();
          if (existing) {
            const result = await this.db.update(businessInfo).set({
              name: info.name,
              address: info.address,
              accountId: info.accountId,
              locationId: info.locationId,
              refreshToken: safeEncrypt(info.refreshToken),
              lastSync: info.lastSync || null
            }).where(eq(businessInfo.id, existing.id)).returning();
            console.log("\u2705 Business info updated successfully");
            return {
              ...result[0],
              refreshToken: safeDecrypt(result[0].refreshToken)
            };
          } else {
            const result = await this.db.insert(businessInfo).values({
              ...info,
              refreshToken: safeEncrypt(info.refreshToken),
              lastSync: info.lastSync || null
            }).returning();
            console.log("\u2705 Business info created successfully");
            return {
              ...result[0],
              refreshToken: safeDecrypt(result[0].refreshToken)
            };
          }
        } catch (error) {
          console.error("\u274C Error creating/updating business info:", error);
          throw new Error("Failed to save business information");
        }
      }
      async getReviews(filters) {
        try {
          let query2 = this.db.select().from(reviews);
          const conditions = [];
          if (filters?.rating && filters.rating >= 1 && filters.rating <= 5) {
            conditions.push(eq(reviews.rating, filters.rating));
          }
          if (filters?.sentiment && ["positive", "neutral", "negative"].includes(filters.sentiment)) {
            conditions.push(eq(reviews.sentiment, filters.sentiment));
          }
          if (conditions.length > 0) {
            query2 = query2.where(and(...conditions));
          }
          query2 = query2.orderBy(desc(reviews.reviewDate));
          const limit = Math.min(filters?.limit || 20, 100);
          const offset = Math.max(filters?.offset || 0, 0);
          query2 = query2.limit(limit);
          if (offset > 0) {
            query2 = query2.offset(offset);
          }
          const result = await query2;
          return result;
        } catch (error) {
          console.error("\u274C Error getting reviews:", error);
          throw new Error("Failed to retrieve reviews");
        }
      }
      async getReviewByGoogleId(googleId) {
        try {
          if (!googleId) {
            throw new Error("Google ID is required");
          }
          const result = await this.db.select().from(reviews).where(eq(reviews.googleId, googleId)).limit(1);
          return result[0];
        } catch (error) {
          console.error("\u274C Error getting review by Google ID:", error);
          throw new Error("Failed to retrieve review");
        }
      }
      async createReview(insertReview) {
        try {
          if (!insertReview.googleId || !insertReview.reviewerName || !insertReview.reviewDate) {
            throw new Error("Missing required review fields");
          }
          if (insertReview.rating < 1 || insertReview.rating > 5) {
            throw new Error("Rating must be between 1 and 5");
          }
          if (!["positive", "neutral", "negative"].includes(insertReview.sentiment)) {
            throw new Error("Invalid sentiment value");
          }
          const result = await this.db.insert(reviews).values(insertReview).returning();
          console.log("\u2705 Review created successfully");
          return result[0];
        } catch (error) {
          console.error("\u274C Error creating review:", error);
          if (error.message?.includes("UNIQUE constraint failed")) {
            throw new Error("Review already exists");
          }
          throw new Error("Failed to create review");
        }
      }
      async updateReview(id, updates) {
        try {
          if (!id || id <= 0) {
            throw new Error("Valid review ID is required");
          }
          const result = await this.db.update(reviews).set(updates).where(eq(reviews.id, id)).returning();
          if (result.length === 0) {
            throw new Error("Review not found");
          }
          console.log("\u2705 Review updated successfully");
          return result[0];
        } catch (error) {
          console.error("\u274C Error updating review:", error);
          throw new Error("Failed to update review");
        }
      }
      async getReviewStats() {
        const allReviews = await this.db.select().from(reviews);
        if (allReviews.length === 0) {
          return {
            totalReviews: 0,
            averageRating: 0,
            ratingCounts: [0, 0, 0, 0, 0],
            sentimentCounts: { positive: 0, neutral: 0, negative: 0 },
            responseRate: 0,
            thisMonth: 0
          };
        }
        const totalReviews = allReviews.length;
        const averageRating = allReviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
        const ratingCounts = [0, 0, 0, 0, 0];
        allReviews.forEach((r) => {
          if (r.rating >= 1 && r.rating <= 5) {
            ratingCounts[r.rating - 1]++;
          }
        });
        const sentimentCounts = allReviews.reduce((acc, r) => {
          acc[r.sentiment]++;
          return acc;
        }, { positive: 0, neutral: 0, negative: 0 });
        const reviewsWithResponses = allReviews.filter((r) => r.responseComment).length;
        const responseRate = totalReviews > 0 ? reviewsWithResponses / totalReviews * 100 : 0;
        const thisMonth = allReviews.filter((r) => {
          const reviewDate = new Date(r.reviewDate);
          const now = /* @__PURE__ */ new Date();
          return reviewDate.getMonth() === now.getMonth() && reviewDate.getFullYear() === now.getFullYear();
        }).length;
        return {
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          ratingCounts,
          sentimentCounts,
          responseRate: Math.round(responseRate),
          thisMonth
        };
      }
      async clearAllData() {
        await this.db.delete(reviews);
        await this.db.delete(businessInfo);
        await this.db.delete(users);
        console.log("\u{1F5D1}\uFE0F All data cleared from database");
      }
    };
    MemStorage = class {
      users;
      business;
      reviewsList;
      currentId;
      currentReviewId;
      constructor() {
        this.users = /* @__PURE__ */ new Map();
        this.business = void 0;
        this.reviewsList = [];
        this.currentId = 1;
        this.currentReviewId = 1;
        console.log("Storage initialized - ready for live Google Business data");
      }
      async getUser(id) {
        return this.users.get(id);
      }
      async getUserById(id) {
        return this.getUser(id);
      }
      async getUserByUsername(username) {
        return Array.from(this.users.values()).find(
          (user) => user.username === username
        );
      }
      async getUserByEmail(email) {
        return Array.from(this.users.values()).find(
          (user) => user.email === email
        );
      }
      async createUser(insertUser) {
        const id = this.currentId++;
        const user = { ...insertUser, id };
        this.users.set(id, user);
        return id;
      }
      async getBusinessInfo() {
        if (this.business) {
          return {
            ...this.business,
            refreshToken: safeDecrypt(this.business.refreshToken)
          };
        }
        return this.business;
      }
      async createOrUpdateBusinessInfo(info) {
        const businessInfo2 = {
          id: this.business?.id || 1,
          name: info.name,
          address: info.address ?? null,
          accountId: info.accountId,
          locationId: info.locationId,
          refreshToken: safeEncrypt(info.refreshToken) ?? null,
          lastSync: info.lastSync ?? null
        };
        this.business = businessInfo2;
        return {
          ...businessInfo2,
          refreshToken: safeDecrypt(businessInfo2.refreshToken)
        };
      }
      async getReviews(filters) {
        let filtered = [...this.reviewsList];
        if (filters?.rating) {
          filtered = filtered.filter((r) => r.rating === filters.rating);
        }
        if (filters?.sentiment) {
          filtered = filtered.filter((r) => r.sentiment === filters.sentiment);
        }
        filtered.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime());
        const offset = filters?.offset || 0;
        const limit = filters?.limit || 20;
        return filtered.slice(offset, offset + limit);
      }
      async getReviewByGoogleId(googleId) {
        return this.reviewsList.find((r) => r.googleId === googleId);
      }
      async createReview(insertReview) {
        const id = this.currentReviewId++;
        const review = {
          id,
          googleId: insertReview.googleId,
          rating: insertReview.rating,
          comment: insertReview.comment ?? null,
          reviewerName: insertReview.reviewerName,
          reviewDate: insertReview.reviewDate,
          responseComment: insertReview.responseComment ?? null,
          responseDate: insertReview.responseDate ?? null,
          sentiment: insertReview.sentiment,
          fetchedAt: insertReview.fetchedAt
        };
        this.reviewsList.push(review);
        return review;
      }
      async updateReview(id, updates) {
        const index = this.reviewsList.findIndex((r) => r.id === id);
        if (index === -1) {
          throw new Error("Review not found");
        }
        this.reviewsList[index] = { ...this.reviewsList[index], ...updates };
        return this.reviewsList[index];
      }
      async getReviewStats() {
        const reviews2 = this.reviewsList;
        const totalReviews = reviews2.length;
        if (totalReviews === 0) {
          return {
            totalReviews: 0,
            averageRating: 0,
            ratingCounts: [0, 0, 0, 0, 0],
            sentimentCounts: { positive: 0, neutral: 0, negative: 0 },
            responseRate: 0,
            thisMonth: 0
          };
        }
        const averageRating = reviews2.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
        const ratingCounts = [0, 0, 0, 0, 0];
        reviews2.forEach((r) => {
          if (r.rating >= 1 && r.rating <= 5) {
            ratingCounts[r.rating - 1]++;
          }
        });
        const sentimentCounts = reviews2.reduce((acc, r) => {
          acc[r.sentiment]++;
          return acc;
        }, { positive: 0, neutral: 0, negative: 0 });
        const reviewsWithResponses = reviews2.filter((r) => r.responseComment).length;
        const responseRate = totalReviews > 0 ? reviewsWithResponses / totalReviews * 100 : 0;
        const thisMonth = reviews2.filter((r) => {
          const reviewDate = new Date(r.reviewDate);
          const now = /* @__PURE__ */ new Date();
          return reviewDate.getMonth() === now.getMonth() && reviewDate.getFullYear() === now.getFullYear();
        }).length;
        return {
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          ratingCounts,
          sentimentCounts,
          responseRate: Math.round(responseRate),
          thisMonth
        };
      }
      async clearAllData() {
        this.users.clear();
        this.business = void 0;
        this.reviewsList = [];
        this.currentId = 1;
        this.currentReviewId = 1;
        console.log("\u{1F5D1}\uFE0F All data cleared from memory storage");
      }
    };
    try {
      storage = new SQLiteStorage();
      console.log("Using SQLite storage");
    } catch (error) {
      console.warn("Failed to initialize SQLite storage, falling back to memory storage:", error);
      storage = new MemStorage();
    }
  }
});

// server/index.ts
import { config as config2 } from "dotenv";
import express2 from "express";

// server/routes.ts
init_storage();
import { createServer } from "http";

// server/services/google-auth.ts
import { google } from "googleapis";
var GoogleAuthService = class {
  oauth2Client;
  retryOptions = {
    maxRetries: 3,
    baseDelay: 1e3,
    maxDelay: 1e4
  };
  constructor() {
    console.log("\u{1F527} Initializing Google Auth Service...");
    console.log("GOOGLE_REDIRECT_URI:", process.env.GOOGLE_REDIRECT_URI);
    console.log("REPLIT_DOMAINS:", process.env.REPLIT_DOMAINS);
    const redirectUri = process.env.GOOGLE_REDIRECT_URI || (process.env.REPLIT_DOMAINS ? `https://${process.env.REPLIT_DOMAINS.split(",")[0]}/api/auth/callback` : "http://localhost:8081/api/auth/google/callback");
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    if (!clientId || !clientSecret) {
      console.error("\u274C CRITICAL SECURITY ERROR: Google OAuth credentials not found in environment variables");
      console.error("\u274C Required: GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET");
      console.error("\u274C Application cannot start without proper credentials");
      throw new Error("Missing required Google OAuth credentials in environment variables");
    }
    console.log("\u2705 OAuth Redirect URI:", redirectUri);
    console.log("\u2705 Google Client ID loaded:", clientId.substring(0, 10) + "...");
    this.oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      redirectUri
    );
  }
  generateAuthUrl() {
    const scopes = [
      "https://www.googleapis.com/auth/business.manage"
    ];
    console.log("\u{1F510} Generating OAuth URL with scopes:", scopes);
    return this.oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      prompt: "consent"
    });
  }
  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  async retryWithBackoff(operation, context) {
    let lastError;
    for (let attempt = 0; attempt <= this.retryOptions.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = Math.min(
            this.retryOptions.baseDelay * Math.pow(2, attempt - 1),
            this.retryOptions.maxDelay
          );
          console.log(`\u23F3 Retrying ${context} in ${delay}ms (attempt ${attempt + 1}/${this.retryOptions.maxRetries + 1})`);
          await this.delay(delay);
        }
        return await operation();
      } catch (error) {
        lastError = error;
        console.log(`\u274C ${context} failed (attempt ${attempt + 1}):`, error.message);
        if (error.message?.includes("invalid_grant") || error.message?.includes("unauthorized") || error.message?.includes("forbidden")) {
          throw error;
        }
        if (attempt === this.retryOptions.maxRetries) {
          throw error;
        }
      }
    }
    throw lastError;
  }
  async getReviews(locationName) {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });
      const response = await this.oauth2Client.request({
        url: `https://mybusinessreviews.googleapis.com/v1/${locationName}/reviews`,
        method: "GET",
        params: {
          pageSize: 50,
          orderBy: "createTime desc"
        }
      });
      const reviews2 = response.data.reviews || [];
      console.log(`\u2705 Successfully fetched ${reviews2.length} reviews`);
      return reviews2;
    }, "fetch reviews").catch((error) => {
      console.error("\u274C Error getting reviews:", error.message);
      if (error.message?.includes("Quota exceeded") || error.message?.includes("quotaExceeded") || error.status === 429) {
        console.log("\u26A0\uFE0F Google API quota exceeded for reviews. Returning empty array...");
        return [];
      }
      if (error.status === 403 || error.message?.includes("permission")) {
        throw new Error("Insufficient permissions to access reviews. Please ensure your Google Business Profile has review access enabled.");
      }
      if (error.status === 404) {
        throw new Error("Business location not found. Please verify your Google Business Profile setup.");
      }
      throw new Error(`Unable to fetch reviews: ${error.message}`);
    });
  }
  async getTokens(code) {
    return this.retryWithBackoff(async () => {
      const { tokens } = await this.oauth2Client.getToken(code);
      this.oauth2Client.setCredentials(tokens);
      console.log("\u2705 Successfully obtained OAuth tokens");
      return tokens;
    }, "get OAuth tokens").catch((error) => {
      console.error("\u274C Error getting tokens:", error.message);
      if (error.message?.includes("invalid_grant")) {
        throw new Error("Authorization code expired or already used. Please try authorizing again.");
      }
      if (error.message?.includes("invalid_client")) {
        throw new Error("Invalid OAuth client configuration. Please check your Google API credentials.");
      }
      throw new Error(`OAuth token exchange failed: ${error.message}`);
    });
  }
  setCredentials(tokens) {
    this.oauth2Client.setCredentials(tokens);
    console.log("\u2705 OAuth credentials set");
  }
  async refreshAccessToken() {
    return this.retryWithBackoff(async () => {
      const { token } = await this.oauth2Client.getAccessToken();
      console.log("\u2705 Access token refreshed successfully");
      return token;
    }, "refresh access token").catch((error) => {
      console.error("\u274C Error refreshing access token:", error.message);
      if (error.message?.includes("invalid_grant")) {
        throw new Error("Refresh token expired. Please re-authorize the application.");
      }
      throw new Error(`Token refresh failed: ${error.message}`);
    });
  }
  getAuthClient() {
    return this.oauth2Client;
  }
  async getBusinessAccounts() {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });
      console.log("\u{1F50D} Attempting to fetch business accounts...");
      console.log("\u{1F50D} Using API endpoint: https://mybusinessaccountmanagement.googleapis.com/v1/accounts");
      const response = await this.oauth2Client.request({
        url: "https://mybusinessaccountmanagement.googleapis.com/v1/accounts",
        method: "GET"
      });
      const accounts = response.data.accounts || [];
      console.log(`\u2705 Successfully fetched ${accounts.length} business accounts`);
      if (accounts.length > 0) {
        accounts.forEach((account, index) => {
          console.log(`\u{1F4CB} Account ${index + 1}:`, {
            name: account.name,
            accountName: account.accountName,
            type: account.type,
            role: account.role
          });
        });
      } else {
        console.log("\u26A0\uFE0F No business accounts found. This could mean:");
        console.log("   1. The Google account has no Business Profiles");
        console.log("   2. The account lacks sufficient permissions");
        console.log("   3. The Business Profile API is not properly enabled");
        console.log("   4. The OAuth scope is insufficient");
      }
      return accounts;
    }, "fetch business accounts").catch((error) => {
      console.error("\u274C Error getting business accounts:", error.message);
      console.error("\u274C Error status:", error.status);
      console.error("\u274C Error details:", error.response?.data || "No additional details");
      if (error.message?.includes("Quota exceeded") || error.message?.includes("quotaExceeded") || error.status === 429) {
        console.log("\u{1F6AB} QUOTA EXHAUSTED: Google Business Profile API quota exceeded.");
        console.log("\u{1F4CB} SOLUTION NEEDED:");
        console.log("   1. Go to Google Cloud Console > APIs & Services > Quotas");
        console.log('   2. Search for "My Business" APIs');
        console.log("   3. Request quota increases for:");
        console.log("      - mybusinessaccountmanagement.googleapis.com");
        console.log("      - mybusinessbusinessinformation.googleapis.com");
        console.log("      - mybusinessreviews.googleapis.com");
        console.log('   4. Increase "Requests per minute" to 60+ (currently 1-10)');
        console.log('   5. Increase "Requests per day" to 10,000+ (currently 100-1000)');
        console.log("\u23F0 Quota resets at midnight Pacific Time (Google's timezone)");
        console.log(`\u{1F550} Current time: ${(/* @__PURE__ */ new Date()).toISOString()}`);
        console.log("\u26A0\uFE0F Returning empty array due to quota limits...");
        return [];
      }
      if (error.status === 403) {
        console.log("\u{1F6AB} Permission denied. Possible causes:");
        console.log("   1. Insufficient Business Profile permissions (need Owner/Manager)");
        console.log("   2. Business Profile API not enabled in Google Cloud Console");
        console.log("   3. OAuth scope insufficient for business account access");
        throw new Error("Insufficient permissions to access business accounts. Please ensure you have a Google Business Profile and the necessary permissions.");
      }
      if (error.status === 404) {
        console.log("\u{1F50D} API endpoint not found. This might indicate:");
        console.log("   1. Wrong API version or endpoint");
        console.log("   2. Business Profile API not enabled");
        throw new Error("Business Profile API endpoint not accessible. Please check API configuration.");
      }
      throw new Error(`Unable to access business accounts: ${error.message}`);
    });
  }
  // Alternative method using Google My Business API (fallback)
  async getBusinessAccountsAlternative() {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });
      console.log("\u{1F504} Trying alternative Google My Business API approach...");
      const mybusiness = google.mybusinessbusinessinformation("v1");
      const response = await mybusiness.accounts.list();
      const accounts = response.data.accounts || [];
      console.log(`\u2705 Alternative API: Successfully fetched ${accounts.length} business accounts`);
      if (accounts.length > 0) {
        accounts.forEach((account, index) => {
          console.log(`\u{1F4CB} Alt Account ${index + 1}:`, {
            name: account.name,
            accountName: account.accountName,
            type: account.type
          });
        });
      }
      return accounts;
    }, "fetch business accounts (alternative)").catch((error) => {
      console.error("\u274C Alternative API also failed:", error.message);
      throw error;
    });
  }
  async getBusinessLocations(accountName) {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });
      const response = await this.oauth2Client.request({
        url: `https://mybusinessbusinessinformation.googleapis.com/v1/${accountName}/locations`,
        method: "GET",
        params: {
          pageSize: 100,
          readMask: "name,title,storefrontAddress,websiteUri,phoneNumbers"
        }
      });
      const locations = response.data.locations || [];
      console.log(`\u2705 Successfully fetched ${locations.length} business locations`);
      return locations;
    }, "fetch business locations").catch((error) => {
      console.error("\u274C Error getting business locations:", error.message);
      if (error.message?.includes("Quota exceeded") || error.message?.includes("quotaExceeded") || error.status === 429) {
        console.log("\u26A0\uFE0F Google API quota exceeded for locations. Returning empty array...");
        return [];
      }
      if (error.status === 403) {
        throw new Error("Insufficient permissions to access business locations. Please ensure your Google Business Profile is verified.");
      }
      if (error.status === 404) {
        throw new Error("Business account not found. Please verify your Google Business Profile setup.");
      }
      throw new Error(`Unable to find business locations: ${error.message}`);
    });
  }
  async postReviewResponse(reviewName, responseComment) {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });
      const response = await this.oauth2Client.request({
        url: `https://mybusinessreviews.googleapis.com/v1/${reviewName}/reply`,
        method: "PUT",
        data: {
          comment: responseComment
        }
      });
      console.log("\u2705 Successfully posted review response");
      return response.data;
    }, "post review response").catch((error) => {
      console.error("\u274C Error posting review response:", error.message);
      if (error.message?.includes("Quota exceeded") || error.message?.includes("quotaExceeded") || error.status === 429) {
        throw new Error("Google API quota exceeded. Please try again later.");
      }
      if (error.status === 403) {
        throw new Error("Insufficient permissions to respond to reviews. Please ensure your Google Business Profile has response permissions enabled.");
      }
      if (error.status === 404) {
        throw new Error("Review not found or no longer available for response.");
      }
      throw new Error(`Unable to post response: ${error.message}`);
    });
  }
};
var _googleAuthService = null;
var googleAuthService = {
  get instance() {
    if (!_googleAuthService) {
      _googleAuthService = new GoogleAuthService();
    }
    return _googleAuthService;
  },
  // Proxy methods to the instance
  generateAuthUrl() {
    return this.instance.generateAuthUrl();
  },
  async getTokens(code) {
    return this.instance.getTokens(code);
  },
  setCredentials(tokens) {
    return this.instance.setCredentials(tokens);
  },
  async refreshAccessToken() {
    return this.instance.refreshAccessToken();
  },
  getAuthClient() {
    return this.instance.getAuthClient();
  },
  async getBusinessAccounts() {
    return this.instance.getBusinessAccounts();
  },
  async getBusinessAccountsAlternative() {
    return this.instance.getBusinessAccountsAlternative();
  },
  async getBusinessLocations(accountName) {
    return this.instance.getBusinessLocations(accountName);
  },
  async getReviews(locationName) {
    return this.instance.getReviews(locationName);
  },
  async postReviewResponse(reviewName, responseComment) {
    return this.instance.postReviewResponse(reviewName, responseComment);
  }
};

// server/services/review-fetcher.ts
init_storage();
var ReviewFetcherService = class {
  lastSyncAttempt = null;
  syncInProgress = false;
  quotaExceededUntil = null;
  isQuotaExceeded() {
    if (!this.quotaExceededUntil) return false;
    const now = /* @__PURE__ */ new Date();
    const isExceeded = now < this.quotaExceededUntil;
    if (!isExceeded && this.quotaExceededUntil) {
      console.log("\u2705 Quota limit period has expired, ready to retry");
      this.quotaExceededUntil = null;
    }
    return isExceeded;
  }
  setQuotaExceeded(durationMinutes = 60) {
    this.quotaExceededUntil = new Date(Date.now() + durationMinutes * 60 * 1e3);
    console.log(`\u26A0\uFE0F Google API quota exceeded. Will retry after ${this.quotaExceededUntil.toLocaleTimeString()}`);
    console.log(`\u2139\uFE0F This is normal - Google Business Profile API has strict quota limits`);
    console.log(`\u2139\uFE0F Your 163 reviews exist and will be fetched once quota resets`);
  }
  getQuotaStatus() {
    const exceeded = this.isQuotaExceeded();
    return {
      exceeded,
      retryAfter: this.quotaExceededUntil,
      message: exceeded ? `Quota exceeded until ${this.quotaExceededUntil?.toLocaleTimeString()}. Your reviews will be fetched automatically when quota resets.` : "Quota available - ready to fetch reviews"
    };
  }
  async fetchReviews(locationName, refreshToken) {
    try {
      if (this.isQuotaExceeded()) {
        console.log("\u26A0\uFE0F Skipping review fetch due to quota limits");
        return [];
      }
      if (!locationName || !refreshToken) {
        throw new Error("Location name and refresh token are required");
      }
      if (locationName === "pending-fetch" || locationName === "disconnected") {
        throw new Error("Business location not found. Please verify your Google Business Profile setup.");
      }
      console.log("\u{1F504} Starting review fetch for location:", locationName);
      googleAuthService.setCredentials({ refresh_token: refreshToken });
      await googleAuthService.refreshAccessToken();
      const fetchedReviews = await googleAuthService.getReviews(locationName);
      const newReviews = [];
      const currentTime = /* @__PURE__ */ new Date();
      console.log(`\u{1F4E5} Processing ${fetchedReviews.length} reviews from Google`);
      if (fetchedReviews.length === 0) {
        console.log("\u2139\uFE0F No reviews found from Google API");
        return [];
      }
      for (const review of fetchedReviews) {
        try {
          if (!review.name) {
            console.log("\u26A0\uFE0F Skipping review without Google ID");
            continue;
          }
          const reviewData = {
            googleId: review.name,
            rating: Math.max(1, Math.min(5, review.starRating || 1)),
            // Ensure rating is 1-5
            comment: review.comment || "",
            reviewerName: review.reviewer?.displayName || "Anonymous",
            reviewDate: new Date(review.createTime || Date.now()).toISOString(),
            responseComment: review.reviewReply?.comment || null,
            responseDate: review.reviewReply ? new Date(review.reviewReply.updateTime || Date.now()).toISOString() : null,
            sentiment: this.determineSentiment(review.starRating || 1, review.comment || ""),
            fetchedAt: currentTime.toISOString()
          };
          const existing = await storage.getReviewByGoogleId(reviewData.googleId);
          if (!existing) {
            await storage.createReview(reviewData);
            newReviews.push(reviewData);
            console.log(`\u2705 New review added: ${reviewData.rating}\u2B50 by ${reviewData.reviewerName}`);
          } else {
            let needsUpdate = false;
            const updates = {};
            if (reviewData.responseComment && reviewData.responseComment !== existing.responseComment) {
              updates.responseComment = reviewData.responseComment;
              updates.responseDate = reviewData.responseDate;
              needsUpdate = true;
            }
            if (needsUpdate) {
              await storage.updateReview(existing.id, updates);
              console.log(`\u{1F504} Updated review response for: ${reviewData.reviewerName}`);
            }
          }
        } catch (reviewError) {
          console.error("\u274C Error processing individual review:", reviewError);
        }
      }
      console.log(`\u2705 Review fetch completed. ${newReviews.length} new reviews added`);
      return newReviews;
    } catch (error) {
      console.error("\u274C Error fetching reviews:", error.message);
      if (error.message?.includes("Quota exceeded") || error.message?.includes("quotaExceeded") || error.status === 429) {
        this.setQuotaExceeded(60);
        return [];
      }
      if (error.message?.includes("invalid_grant") || error.message?.includes("unauthorized")) {
        throw new Error("Authentication failed. Please reconnect your Google account.");
      }
      if (error.status === 403) {
        throw new Error("Insufficient permissions to access reviews. Please check your Google Business Profile permissions.");
      }
      throw new Error(`Failed to fetch reviews: ${error.message}`);
    }
  }
  determineSentiment(rating, comment = "") {
    let sentimentFromRating;
    if (rating >= 4) sentimentFromRating = "positive";
    else if (rating <= 2) sentimentFromRating = "negative";
    else sentimentFromRating = "neutral";
    if (!comment.trim()) return sentimentFromRating;
    const text2 = comment.toLowerCase();
    const positiveWords = ["great", "excellent", "amazing", "wonderful", "fantastic", "love", "perfect", "best", "awesome", "outstanding", "superb", "brilliant"];
    const negativeWords = ["terrible", "awful", "horrible", "worst", "hate", "disgusting", "disappointing", "bad", "poor", "unacceptable", "pathetic", "useless"];
    const positiveCount = positiveWords.filter((word) => text2.includes(word)).length;
    const negativeCount = negativeWords.filter((word) => text2.includes(word)).length;
    if (positiveCount > negativeCount + 1) return "positive";
    if (negativeCount > positiveCount + 1) return "negative";
    return sentimentFromRating;
  }
  async syncReviews() {
    if (this.syncInProgress) {
      console.log("\u26A0\uFE0F Sync already in progress, skipping...");
      return { success: false, newReviews: 0, error: "Sync already in progress" };
    }
    if (this.isQuotaExceeded()) {
      console.log("\u26A0\uFE0F Skipping sync due to quota limits");
      return { success: false, newReviews: 0, error: "API quota exceeded", quotaExceeded: true };
    }
    this.syncInProgress = true;
    this.lastSyncAttempt = /* @__PURE__ */ new Date();
    try {
      console.log("\u{1F504} Starting review sync...");
      const businessInfo2 = await storage.getBusinessInfo();
      if (!businessInfo2 || !businessInfo2.locationId || !businessInfo2.refreshToken) {
        console.log("\u274C Business not configured for sync");
        return { success: false, newReviews: 0, error: "Business not configured" };
      }
      if (businessInfo2.locationId === "pending-fetch" || businessInfo2.accountId === "pending-fetch" || businessInfo2.locationId === "disconnected" || businessInfo2.accountId === "disconnected") {
        console.log("\u23F3 Business data still being fetched from Google or disconnected. Skipping sync...");
        return { success: false, newReviews: 0, error: "Business location not found. Please verify your Google Business Profile setup." };
      }
      console.log(`\u{1F4CD} Syncing reviews for location: ${businessInfo2.name}`);
      const newReviews = await this.fetchReviews(businessInfo2.locationId, businessInfo2.refreshToken);
      await storage.createOrUpdateBusinessInfo({
        ...businessInfo2,
        lastSync: (/* @__PURE__ */ new Date()).toISOString()
      });
      console.log(`\u2705 Review sync completed successfully. ${newReviews.length} new reviews`);
      return { success: true, newReviews: newReviews.length };
    } catch (error) {
      console.error("\u274C Review sync failed:", error.message);
      const isQuotaError = error.message?.includes("quota") || error.message?.includes("Quota") || error.status === 429;
      return {
        success: false,
        newReviews: 0,
        error: error.message,
        quotaExceeded: isQuotaError
      };
    } finally {
      this.syncInProgress = false;
    }
  }
};
var reviewFetcherService = new ReviewFetcherService();

// server/routes.ts
init_schema();
import cron from "node-cron";

// server/services/auth.ts
init_storage();
import { config } from "dotenv";
import * as jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
config();
var JWT_SECRET = process.env.JWT_SECRET;
var JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "24h";
if (!JWT_SECRET || JWT_SECRET.length < 32) {
  if (process.env.NODE_ENV !== "test") {
    console.error("\u274C CRITICAL SECURITY ERROR: JWT_SECRET not found or too short in environment variables");
    console.error("\u274C Required: JWT_SECRET with minimum 32 characters");
    console.error("\u274C Generate using: openssl rand -base64 32");
    throw new Error("Missing or invalid JWT_SECRET in environment variables");
  }
}
var AuthService = class {
  /**
   * Hash a password using bcrypt
   */
  static async hashPassword(password) {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }
  /**
   * Verify a password against its hash
   */
  static async verifyPassword(password, hash) {
    return bcrypt.compare(password, hash);
  }
  /**
   * Generate a JWT token for a user
   */
  static generateToken(user) {
    const payload = {
      userId: user.id,
      email: user.email
    };
    const secret = JWT_SECRET || "test-secret-for-testing-only";
    return jwt.sign(payload, secret, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: "HotelReviewHub",
      audience: "HotelReviewHub-Users"
    });
  }
  /**
   * Verify and decode a JWT token
   */
  static verifyToken(token) {
    try {
      const secret = JWT_SECRET || "test-secret-for-testing-only";
      return jwt.verify(token, secret, {
        issuer: "HotelReviewHub",
        audience: "HotelReviewHub-Users"
      });
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error("Token expired");
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error("Invalid token");
      } else {
        throw new Error("Token verification failed");
      }
    }
  }
  /**
   * Register a new user
   */
  static async registerUser(email, password, name) {
    const existingUser = await storage.getUserByEmail(email);
    if (existingUser) {
      throw new Error("User already exists with this email");
    }
    const hashedPassword = await this.hashPassword(password);
    const userId = await storage.createUser({
      username: email,
      // Use email as username for now
      email,
      password: hashedPassword,
      name
    });
    return {
      id: userId,
      email,
      name
    };
  }
  /**
   * Login a user
   */
  static async loginUser(email, password) {
    const user = await storage.getUserByEmail(email);
    if (!user) {
      throw new Error("Invalid email or password");
    }
    const isValidPassword = await this.verifyPassword(password, user.password);
    if (!isValidPassword) {
      throw new Error("Invalid email or password");
    }
    const authUser = {
      id: user.id,
      email: user.email,
      name: user.name
    };
    const token = this.generateToken(authUser);
    return {
      user: authUser,
      token
    };
  }
};
var authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];
    if (!token) {
      return res.status(401).json({
        error: "Access denied",
        message: "Authentication token required"
      });
    }
    const decoded = AuthService.verifyToken(token);
    const user = await storage.getUserById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        error: "Access denied",
        message: "User not found"
      });
    }
    req.user = {
      id: user.id,
      email: user.email,
      name: user.name
    };
    next();
  } catch (error) {
    console.error("Authentication error:", error);
    return res.status(401).json({
      error: "Access denied",
      message: error instanceof Error ? error.message : "Invalid token"
    });
  }
};
var optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];
    if (token) {
      const decoded = AuthService.verifyToken(token);
      const user = await storage.getUserById(decoded.userId);
      if (user) {
        req.user = {
          id: user.id,
          email: user.email,
          name: user.name
        };
      }
    }
    next();
  } catch (error) {
    console.warn("Optional authentication failed:", error);
    next();
  }
};

// server/middleware/validation.ts
import { body, param, query, validationResult } from "express-validator";
import DOMPurify from "dompurify";
import { JSDOM } from "jsdom";
var window = new JSDOM("").window;
var purify = DOMPurify(window);
var handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: "Validation failed",
      details: errors.array().map((error) => ({
        field: error.type === "field" ? error.path : "unknown",
        message: error.msg,
        value: error.type === "field" ? error.value : void 0
      }))
    });
  }
  next();
};
var sanitizeHtml = (html) => {
  return purify.sanitize(html, {
    ALLOWED_TAGS: [],
    // No HTML tags allowed
    ALLOWED_ATTR: []
  });
};
var sanitizeBody = (req, res, next) => {
  if (req.body && typeof req.body === "object") {
    for (const key in req.body) {
      if (typeof req.body[key] === "string") {
        req.body[key] = sanitizeHtml(req.body[key]);
      }
    }
  }
  next();
};
var validateRegister = [
  body("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address").isLength({ max: 255 }).withMessage("Email must be less than 255 characters"),
  body("password").isLength({ min: 8, max: 128 }).withMessage("Password must be between 8 and 128 characters").matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).withMessage("Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character"),
  body("name").trim().isLength({ min: 1, max: 100 }).withMessage("Name must be between 1 and 100 characters").matches(/^[a-zA-Z\s'-]+$/).withMessage("Name can only contain letters, spaces, hyphens, and apostrophes"),
  sanitizeBody,
  handleValidationErrors
];
var validateLogin = [
  body("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address"),
  body("password").isLength({ min: 1 }).withMessage("Password is required"),
  sanitizeBody,
  handleValidationErrors
];
var validateReviewResponse = [
  body("response").trim().isLength({ min: 1, max: 4096 }).withMessage("Response must be between 1 and 4096 characters"),
  sanitizeBody,
  handleValidationErrors
];
var validateReviewId = [
  param("id").isInt({ min: 1 }).withMessage("Review ID must be a positive integer"),
  handleValidationErrors
];
var validateBusinessInfo = [
  body("name").optional().trim().isLength({ min: 1, max: 255 }).withMessage("Business name must be between 1 and 255 characters"),
  body("address").optional().trim().isLength({ min: 1, max: 500 }).withMessage("Address must be between 1 and 500 characters"),
  body("phone").optional().trim().matches(/^[\+]?[1-9][\d]{0,15}$/).withMessage("Please provide a valid phone number"),
  body("website").optional().trim().isURL({ protocols: ["http", "https"], require_protocol: true }).withMessage("Please provide a valid website URL"),
  sanitizeBody,
  handleValidationErrors
];
var validatePagination = [
  query("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),
  query("offset").optional().isInt({ min: 0 }).withMessage("Offset must be a non-negative integer"),
  query("rating").optional().isInt({ min: 1, max: 5 }).withMessage("Rating must be between 1 and 5"),
  query("sentiment").optional().isIn(["positive", "neutral", "negative"]).withMessage("Sentiment must be positive, neutral, or negative"),
  handleValidationErrors
];
var validateApiKey = [
  body("apiKey").optional().isLength({ min: 10, max: 500 }).withMessage("API key must be between 10 and 500 characters"),
  sanitizeBody,
  handleValidationErrors
];
var validateContentType = (req, res, next) => {
  if (req.method === "POST" || req.method === "PUT" || req.method === "PATCH") {
    if (!req.is("application/json")) {
      return res.status(400).json({
        error: "Invalid Content-Type",
        message: "Content-Type must be application/json"
      });
    }
  }
  next();
};

// server/middleware/security.ts
import helmet from "helmet";
import cors from "cors";
import rateLimit from "express-rate-limit";
var securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://mybusinessbusinessinformation.googleapis.com", "https://mybusiness.googleapis.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      upgradeInsecureRequests: process.env.NODE_ENV === "production" ? [] : null
    }
  },
  hsts: {
    maxAge: 31536e3,
    // 1 year
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: "deny" },
  xssFilter: true,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" }
});
var corsConfig = cors({
  origin: function(origin, callback) {
    if (!origin) return callback(null, true);
    const allowedOrigins = [
      "http://localhost:8081",
      "http://localhost:3000",
      "http://127.0.0.1:8081",
      "http://127.0.0.1:3000"
    ];
    if (process.env.ALLOWED_ORIGINS) {
      allowedOrigins.push(...process.env.ALLOWED_ORIGINS.split(","));
    }
    if (process.env.REPLIT_DOMAINS) {
      const replitDomains = process.env.REPLIT_DOMAINS.split(",");
      replitDomains.forEach((domain) => {
        allowedOrigins.push(`https://${domain}`);
      });
    }
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`\u274C CORS blocked origin: ${origin}`);
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Origin",
    "X-Requested-With",
    "Content-Type",
    "Accept",
    "Authorization",
    "X-CSRF-Token"
  ],
  exposedHeaders: ["X-CSRF-Token"],
  maxAge: 86400
  // 24 hours
});
var generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1e3,
  // 15 minutes
  max: 100,
  // Limit each IP to 100 requests per windowMs
  message: {
    error: "Too many requests",
    message: "Too many requests from this IP, please try again later.",
    retryAfter: "15 minutes"
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`\u274C Rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: "Too many requests",
      message: "Too many requests from this IP, please try again later.",
      retryAfter: "15 minutes"
    });
  }
});
var authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1e3,
  // 15 minutes
  max: 5,
  // Limit each IP to 5 auth requests per windowMs
  message: {
    error: "Too many authentication attempts",
    message: "Too many authentication attempts from this IP, please try again later.",
    retryAfter: "15 minutes"
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true,
  // Don't count successful requests
  handler: (req, res) => {
    console.warn(`\u274C Auth rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: "Too many authentication attempts",
      message: "Too many authentication attempts from this IP, please try again later.",
      retryAfter: "15 minutes"
    });
  }
});
var apiRateLimit = rateLimit({
  windowMs: 60 * 1e3,
  // 1 minute
  max: 30,
  // Limit each IP to 30 API requests per minute
  message: {
    error: "API rate limit exceeded",
    message: "Too many API requests from this IP, please try again later.",
    retryAfter: "1 minute"
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`\u274C API rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: "API rate limit exceeded",
      message: "Too many API requests from this IP, please try again later.",
      retryAfter: "1 minute"
    });
  }
});
var requestLogger = (req, res, next) => {
  const start = Date.now();
  const { method, url, ip } = req;
  const userAgent = req.get("User-Agent") || "Unknown";
  console.log(`\u{1F4E5} ${method} ${url} - IP: ${ip} - UA: ${userAgent.substring(0, 50)}`);
  res.on("finish", () => {
    const duration = Date.now() - start;
    const { statusCode } = res;
    const statusEmoji = statusCode >= 400 ? "\u274C" : statusCode >= 300 ? "\u26A0\uFE0F" : "\u2705";
    console.log(`\u{1F4E4} ${statusEmoji} ${method} ${url} - ${statusCode} - ${duration}ms - IP: ${ip}`);
    if (statusCode === 401) {
      console.warn(`\u{1F512} Unauthorized access attempt: ${method} ${url} - IP: ${ip}`);
    } else if (statusCode === 403) {
      console.warn(`\u{1F6AB} Forbidden access attempt: ${method} ${url} - IP: ${ip}`);
    } else if (statusCode === 429) {
      console.warn(`\u23F0 Rate limit hit: ${method} ${url} - IP: ${ip}`);
    }
  });
  next();
};
var securityErrorHandler = (error, req, res, next) => {
  if (error.message === "Not allowed by CORS") {
    console.error(`\u274C CORS error for ${req.method} ${req.url} from origin: ${req.get("Origin")}`);
    return res.status(403).json({
      error: "CORS policy violation",
      message: "Origin not allowed by CORS policy"
    });
  }
  if (error.type === "entity.too.large") {
    console.error(`\u274C Request too large: ${req.method} ${req.url} - IP: ${req.ip}`);
    return res.status(413).json({
      error: "Request too large",
      message: "Request entity too large"
    });
  }
  if (error.type === "entity.parse.failed") {
    console.error(`\u274C JSON parse error: ${req.method} ${req.url} - IP: ${req.ip}`);
    return res.status(400).json({
      error: "Invalid JSON",
      message: "Request body contains invalid JSON"
    });
  }
  console.error(`\u274C Security error: ${error.message} - ${req.method} ${req.url} - IP: ${req.ip}`);
  if (process.env.NODE_ENV === "production") {
    return res.status(500).json({
      error: "Internal server error",
      message: "An unexpected error occurred"
    });
  }
  next(error);
};
var requestSizeLimit = (maxSize = "10mb") => {
  return (req, res, next) => {
    const contentLength = req.get("Content-Length");
    const maxBytes = parseSize(maxSize);
    if (contentLength && parseInt(contentLength) > maxBytes) {
      console.warn(`\u274C Request too large: ${contentLength} bytes from IP: ${req.ip}`);
      return res.status(413).json({
        error: "Request too large",
        message: `Request size exceeds ${maxSize} limit`
      });
    }
    next();
  };
};
function parseSize(size) {
  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)(b|kb|mb|gb)$/);
  if (!match) return 1024 * 1024;
  const [, num, unit] = match;
  return Math.floor(parseFloat(num) * units[unit]);
}

// server/routes.ts
async function fetchBusinessDataInBackground(refreshToken) {
  let attempts = 0;
  const maxAttempts = 3;
  console.log("\u{1F504} Starting background business data fetch...");
  while (attempts < maxAttempts) {
    try {
      console.log(`Attempting to fetch business data (attempt ${attempts + 1}/${maxAttempts})`);
      googleAuthService.setCredentials({ refresh_token: refreshToken });
      await googleAuthService.refreshAccessToken();
      const accounts = await googleAuthService.getBusinessAccounts();
      if (accounts.length > 0) {
        const account = accounts[0];
        console.log("Successfully fetched business account:", account.accountName);
        const locations = await googleAuthService.getBusinessLocations(account.name);
        if (locations.length > 0) {
          const location = locations[0];
          console.log("Successfully fetched business location:", location.title);
          await storage.createOrUpdateBusinessInfo({
            name: location.title || account.accountName || "Your Business",
            address: location.storefrontAddress?.addressLines?.join(", ") || "Address not available",
            accountId: account.name || "",
            locationId: location.name || "",
            refreshToken,
            lastSync: null
          });
          console.log("Business data updated successfully - triggering review sync");
          setTimeout(async () => {
            const syncResult = await reviewFetcherService.syncReviews();
            console.log("Review sync result:", syncResult);
          }, 1e3);
          return;
        }
      }
      attempts++;
      if (attempts < maxAttempts) {
        const delay = 5e3 * attempts;
        console.log(`Waiting ${delay / 1e3}s before retry...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    } catch (error) {
      attempts++;
      console.log(`Business data fetch attempt ${attempts} failed:`, error.message);
      if (attempts < maxAttempts) {
        const delay = 5e3 * attempts;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }
  console.log("\u274C Business data fetch failed after all attempts");
  try {
    const currentInfo = await storage.getBusinessInfo();
    if (currentInfo && currentInfo.locationId === "pending-fetch") {
      await storage.createOrUpdateBusinessInfo({
        ...currentInfo,
        name: "Business Setup Required",
        address: "Failed to fetch business data from Google. Please try reconnecting your account."
      });
      console.log("Updated business info to indicate setup failure");
    }
  } catch (updateError) {
    console.error("Failed to update business info after fetch failure:", updateError);
  }
}
async function registerRoutes(app2) {
  console.log("\u{1F527} Initializing database...");
  if (process.env.NODE_ENV === "development" && process.env.CLEAR_DATA_ON_START === "true") {
    console.log("\u26A0\uFE0F Development mode: Clearing all data as requested");
    await storage.clearAllData();
  } else {
    console.log("\u2705 Production mode: Preserving existing data");
  }
  cron.schedule("*/30 * * * *", async () => {
    console.log("\u{1F504} Running scheduled review sync...");
    try {
      const result = await reviewFetcherService.syncReviews();
      if (result.success) {
        console.log(`\u2705 Scheduled sync completed: ${result.newReviews} new reviews`);
      } else if (result.quotaExceeded) {
        console.log("\u26A0\uFE0F Scheduled sync skipped due to quota limits");
      } else {
        console.log(`\u274C Scheduled sync failed: ${result.error}`);
      }
    } catch (error) {
      console.error("\u274C Scheduled sync error:", error);
    }
  }, {
    scheduled: true,
    timezone: "UTC"
  });
  app2.get("/api/setup", async (req, res) => {
    try {
      const businessInfo2 = await storage.getBusinessInfo();
      const isSetup = businessInfo2 && businessInfo2.refreshToken && businessInfo2.accountId !== "disconnected" && businessInfo2.locationId !== "disconnected";
      res.json({
        isSetup,
        setupUrl: isSetup ? null : googleAuthService.generateAuthUrl()
      });
    } catch (error) {
      console.log("No business info found (fresh start):", error);
      res.json({
        isSetup: false,
        setupUrl: googleAuthService.generateAuthUrl()
      });
    }
  });
  app2.get("/api/business", optionalAuth, async (req, res) => {
    try {
      const businessInfo2 = await storage.getBusinessInfo();
      const isSetup = businessInfo2 && businessInfo2.refreshToken && businessInfo2.accountId !== "disconnected" && businessInfo2.locationId !== "disconnected";
      res.json({
        businessInfo: businessInfo2,
        isSetup,
        setupUrl: isSetup ? null : googleAuthService.generateAuthUrl()
      });
    } catch (error) {
      console.log("No business info found (fresh start):", error);
      res.json({
        businessInfo: null,
        isSetup: false,
        setupUrl: googleAuthService.generateAuthUrl()
      });
    }
  });
  const handleOAuthCallback = async (req, res) => {
    try {
      const { code, error } = req.query;
      if (error) {
        console.error("OAuth error:", error);
        return res.redirect("/?setup=error&message=" + encodeURIComponent(error));
      }
      if (!code || typeof code !== "string") {
        return res.redirect("/?setup=error&message=" + encodeURIComponent("Missing authorization code"));
      }
      console.log("Processing OAuth callback with code:", code.substring(0, 20) + "...");
      const tokens = await googleAuthService.getTokens(code);
      console.log("Successfully obtained tokens");
      try {
        const accounts = await googleAuthService.getBusinessAccounts();
        console.log("Found business accounts:", accounts.length);
        if (accounts.length === 0) {
          await storage.createOrUpdateBusinessInfo({
            name: "Loading Business Information...",
            address: "Fetching business details from Google...",
            accountId: "pending-fetch",
            locationId: "pending-fetch",
            refreshToken: tokens.refresh_token || "",
            lastSync: null
          });
          console.log("\u2705 Authentication successful - will fetch business data in background");
          setTimeout(async () => {
            await fetchBusinessDataInBackground(tokens.refresh_token || "");
          }, 2e3);
          return res.redirect("/dashboard");
        }
        const account = accounts[0];
        const locations = await googleAuthService.getBusinessLocations(account.name);
        console.log("Found business locations:", locations.length);
        if (locations.length === 0) {
          await storage.createOrUpdateBusinessInfo({
            name: account.accountName || "Your Business",
            address: "Location details pending",
            accountId: account.name || "",
            locationId: "no-location-found",
            refreshToken: tokens.refresh_token || "",
            lastSync: null
          });
          console.log("\u2705 Account connected - dashboard enabled");
          return res.redirect("/dashboard");
        }
        const location = locations[0];
        console.log("Using business location:", location.title);
        await storage.createOrUpdateBusinessInfo({
          name: location.title || "Unknown Business",
          address: location.storefrontAddress?.addressLines?.join(", ") || "",
          accountId: account.name || "",
          locationId: location.name || "",
          refreshToken: tokens.refresh_token || "",
          lastSync: null
        });
        console.log("\u2705 Complete business setup successful");
        try {
          const syncResult = await reviewFetcherService.syncReviews();
          console.log("Initial sync result:", syncResult);
        } catch (syncError) {
          console.log("Initial sync skipped due to quota limits - will retry automatically");
        }
        res.redirect("/dashboard");
      } catch (businessError) {
        console.log("\u26A0\uFE0F Business API quota exceeded - proceeding with basic authentication");
        await storage.createOrUpdateBusinessInfo({
          name: "Your Business Profile",
          address: "Details will load when quota resets",
          accountId: "quota-limited",
          locationId: "quota-limited",
          refreshToken: tokens.refresh_token || "",
          lastSync: null
        });
        console.log("\u2705 Google authentication successful - dashboard ready");
        res.redirect("/dashboard");
      }
    } catch (error) {
      console.error("OAuth callback error:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      res.redirect("/?setup=error&message=" + encodeURIComponent(errorMessage));
    }
  };
  app2.get("/api/auth/callback", handleOAuthCallback);
  app2.get("/api/auth/google/callback", handleOAuthCallback);
  app2.get("/api/reviews", optionalAuth, validatePagination, async (req, res) => {
    try {
      const { rating, sentiment, limit = "20", offset = "0" } = req.query;
      const filters = {
        rating: rating ? parseInt(rating) : void 0,
        sentiment,
        limit: parseInt(limit),
        offset: parseInt(offset)
      };
      const reviews2 = await storage.getReviews(filters);
      res.json(reviews2);
    } catch (error) {
      res.status(500).json({ message: "Failed to get reviews" });
    }
  });
  app2.get("/api/reviews/stats", optionalAuth, async (req, res) => {
    try {
      const stats = await storage.getReviewStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ message: "Failed to get review stats" });
    }
  });
  app2.post("/api/reviews/:id/response", authenticateToken, apiRateLimit, validateContentType, validateReviewId, validateReviewResponse, async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const body2 = updateReviewResponseSchema.parse(req.body);
      const review = await storage.getReviews();
      const targetReview = review.find((r) => r.id === reviewId);
      if (!targetReview) {
        return res.status(404).json({ message: "Review not found" });
      }
      const businessInfo2 = await storage.getBusinessInfo();
      if (!businessInfo2 || !businessInfo2.refreshToken) {
        return res.status(400).json({ message: "Google Business account not configured" });
      }
      googleAuthService.setCredentials({ refresh_token: businessInfo2.refreshToken });
      await googleAuthService.refreshAccessToken();
      try {
        await googleAuthService.postReviewResponse(targetReview.googleId, body2.responseComment);
        console.log(`Posted response to Google for review ${targetReview.googleId}`);
      } catch (googleError) {
        console.error("Failed to post to Google:", googleError);
      }
      const updatedReview = await storage.updateReview(reviewId, {
        responseComment: body2.responseComment,
        responseDate: (/* @__PURE__ */ new Date()).toISOString()
      });
      res.json(updatedReview);
    } catch (error) {
      console.error("Response error:", error);
      res.status(400).json({ message: "Failed to add response" });
    }
  });
  app2.post("/api/sync", authenticateToken, apiRateLimit, async (req, res) => {
    try {
      console.log("\u{1F504} Manual sync requested");
      const result = await reviewFetcherService.syncReviews();
      if (result.success) {
        res.json({
          success: true,
          message: `Successfully synced ${result.newReviews} new reviews`,
          newReviews: result.newReviews
        });
      } else if (result.quotaExceeded) {
        res.json({
          success: false,
          message: "Google API quota exceeded. The system will automatically retry when quota resets. This is normal behavior.",
          quotaExceeded: true,
          retryInfo: "Quotas typically reset every minute for per-minute limits, and daily at midnight Pacific Time for daily limits.",
          explanation: "Your Google Business Profile has 163 reviews, but API quotas prevent fetching them right now."
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.error || "Sync failed"
        });
      }
    } catch (error) {
      console.error("\u274C Manual sync error:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Internal server error"
      });
    }
  });
  app2.get("/api/quota-status", optionalAuth, async (req, res) => {
    try {
      const businessInfo2 = await storage.getBusinessInfo();
      const quotaInfo = reviewFetcherService.getQuotaStatus();
      const isPendingFetch = businessInfo2?.locationId === "pending-fetch" || businessInfo2?.accountId === "pending-fetch";
      const isProperlyConfigured = !!(businessInfo2?.locationId && businessInfo2?.refreshToken && !isPendingFetch);
      const quotaStatus = {
        hasBusinessInfo: !!businessInfo2,
        businessName: businessInfo2?.name || null,
        lastSync: businessInfo2?.lastSync || null,
        isConfigured: isProperlyConfigured,
        quota: quotaInfo,
        message: isPendingFetch ? "Business data is being fetched from Google. Please wait..." : businessInfo2 && isProperlyConfigured ? `Business "${businessInfo2.name}" is configured. ${quotaInfo.message}` : "Business not configured. Please complete authentication first.",
        explanation: isPendingFetch ? "Your Google Business Profile data is being retrieved. This may take a few moments." : isProperlyConfigured ? "Your 163 Google reviews will be fetched once API quota limits reset. This is normal behavior due to Google API restrictions." : "Complete the Google authentication to access your reviews."
      };
      res.json(quotaStatus);
    } catch (error) {
      console.error("\u274C Error checking quota status:", error);
      res.status(500).json({
        error: "Failed to check quota status",
        message: error.message
      });
    }
  });
  app2.post("/api/refresh-business", authenticateToken, apiRateLimit, async (req, res) => {
    try {
      const businessInfo2 = await storage.getBusinessInfo();
      if (!businessInfo2 || !businessInfo2.refreshToken) {
        return res.json({ success: false, error: "No authentication found. Please reconnect your Google account." });
      }
      console.log("\u{1F504} Manual business data refresh requested...");
      await fetchBusinessDataInBackground(businessInfo2.refreshToken);
      const updatedInfo = await storage.getBusinessInfo();
      res.json({ success: true, businessInfo: updatedInfo });
    } catch (error) {
      console.error("Business refresh error:", error);
      res.json({ success: false, error: error.message });
    }
  });
  app2.get("/api/quota-guidance", authenticateToken, async (req, res) => {
    try {
      const businessInfo2 = await storage.getBusinessInfo();
      if (!businessInfo2 || !businessInfo2.refreshToken) {
        return res.json({
          connected: false,
          message: "No Google account connected. Please connect your Google account first.",
          guidance: []
        });
      }
      const guidance = [
        {
          step: 1,
          title: "Go to Google Cloud Console",
          description: "Visit https://console.cloud.google.com/",
          action: "Navigate to your project: ************"
        },
        {
          step: 2,
          title: "Access Quotas & System Limits",
          description: "Go to APIs & Services > Quotas",
          action: "Filter by 'My Business' or search for the APIs below"
        },
        {
          step: 3,
          title: "Request Quota Increases",
          description: "Find and request increases for these APIs:",
          apis: [
            "mybusinessaccountmanagement.googleapis.com",
            "mybusinessbusinessinformation.googleapis.com",
            "mybusinessreviews.googleapis.com"
          ]
        },
        {
          step: 4,
          title: "Increase Specific Quotas",
          description: "Request these quota increases:",
          quotas: [
            "Requests per minute: Increase to 60+ (currently 1-10)",
            "Requests per day: Increase to 10,000+ (currently 100-1000)"
          ]
        },
        {
          step: 5,
          title: "Wait for Approval or Reset",
          description: "Quota increases need Google approval (1-2 business days)",
          alternative: "Or wait for natural quota reset at midnight Pacific Time"
        }
      ];
      res.json({
        connected: true,
        quotaIssue: true,
        message: "Google Business Profile API has very low quota limits by default",
        currentTime: (/* @__PURE__ */ new Date()).toISOString(),
        quotaResetTime: "Midnight Pacific Time (Google's timezone)",
        projectNumber: "************",
        guidance
      });
    } catch (error) {
      res.json({
        connected: false,
        error: error.message,
        message: "Error checking quota status"
      });
    }
  });
  app2.get("/api/test-quota", authenticateToken, async (req, res) => {
    try {
      const businessInfo2 = await storage.getBusinessInfo();
      if (!businessInfo2 || !businessInfo2.refreshToken) {
        return res.json({
          quotaOk: false,
          error: "No authentication found. Please connect your Google account first."
        });
      }
      googleAuthService.setCredentials({ refresh_token: businessInfo2.refreshToken });
      res.json({
        quotaOk: true,
        message: "Authentication ready - but quota limits may apply",
        hasCredentials: true,
        recommendation: "Use /api/quota-guidance for detailed quota increase instructions",
        warning: "Google Business Profile API has very restrictive quota limits by default"
      });
    } catch (error) {
      res.json({
        quotaOk: false,
        error: error.message,
        message: "Authentication error occurred"
      });
    }
  });
  app2.post("/api/logout", optionalAuth, async (_req, res) => {
    try {
      console.log("\u{1F6AA} Logout requested - clearing authentication data");
      await storage.createOrUpdateBusinessInfo({
        name: "Not Connected",
        address: "Please connect your Google Business Profile",
        locationId: "disconnected",
        accountId: "disconnected",
        refreshToken: "",
        lastSync: null
      });
      console.log("\u2705 Successfully logged out and cleared authentication data");
      res.json({
        success: true,
        message: "Successfully logged out. All authentication data has been cleared."
      });
    } catch (error) {
      console.error("\u274C Error during logout:", error);
      res.status(500).json({
        success: false,
        error: "Failed to logout. Please try again."
      });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "../dist/public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/routes/auth.ts
import { Router } from "express";
var router = Router();
router.post("/register", authRateLimit, validateRegister, async (req, res) => {
  try {
    const { email, password, name } = req.body;
    console.log(`\u{1F4DD} Registration attempt for email: ${email}`);
    const user = await AuthService.registerUser(email, password, name);
    const token = AuthService.generateToken(user);
    console.log(`\u2705 User registered successfully: ${email}`);
    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        },
        token
      }
    });
  } catch (error) {
    console.error("\u274C Registration error:", error);
    if (error instanceof Error) {
      if (error.message === "User already exists with this email") {
        return res.status(409).json({
          error: "Registration failed",
          message: "An account with this email already exists"
        });
      }
    }
    res.status(500).json({
      error: "Registration failed",
      message: "An unexpected error occurred during registration"
    });
  }
});
router.post("/login", authRateLimit, validateLogin, async (req, res) => {
  try {
    const { email, password } = req.body;
    console.log(`\u{1F510} Login attempt for email: ${email}`);
    const { user, token } = await AuthService.loginUser(email, password);
    console.log(`\u2705 User logged in successfully: ${email}`);
    res.json({
      success: true,
      message: "Login successful",
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        },
        token
      }
    });
  } catch (error) {
    console.error("\u274C Login error:", error);
    if (error instanceof Error && error.message === "Invalid email or password") {
      return res.status(401).json({
        error: "Login failed",
        message: "Invalid email or password"
      });
    }
    res.status(500).json({
      error: "Login failed",
      message: "An unexpected error occurred during login"
    });
  }
});
router.post("/logout", (req, res) => {
  console.log("\u{1F44B} User logout request");
  res.json({
    success: true,
    message: "Logout successful"
  });
});
router.get("/me", async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];
    if (!token) {
      return res.status(401).json({
        error: "Access denied",
        message: "Authentication token required"
      });
    }
    const decoded = AuthService.verifyToken(token);
    const { storage: storage2 } = await Promise.resolve().then(() => (init_storage(), storage_exports));
    const user = await storage2.getUserById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        error: "Access denied",
        message: "User not found"
      });
    }
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        }
      }
    });
  } catch (error) {
    console.error("\u274C Get user error:", error);
    if (error instanceof Error) {
      if (error.message === "Token expired") {
        return res.status(401).json({
          error: "Token expired",
          message: "Authentication token has expired"
        });
      } else if (error.message === "Invalid token") {
        return res.status(401).json({
          error: "Invalid token",
          message: "Authentication token is invalid"
        });
      }
    }
    res.status(500).json({
      error: "Authentication failed",
      message: "An unexpected error occurred"
    });
  }
});
router.post("/refresh", async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];
    if (!token) {
      return res.status(401).json({
        error: "Access denied",
        message: "Authentication token required"
      });
    }
    let decoded;
    try {
      decoded = AuthService.verifyToken(token);
    } catch (error) {
      if (error instanceof Error && error.message === "Token expired") {
        const jwt2 = __require("jsonwebtoken");
        decoded = jwt2.decode(token);
        if (!decoded || !decoded.exp) {
          throw new Error("Invalid token");
        }
        const tokenAge = Date.now() / 1e3 - decoded.exp;
        if (tokenAge > 7 * 24 * 60 * 60) {
          throw new Error("Token too old for refresh");
        }
      } else {
        throw error;
      }
    }
    const { storage: storage2 } = await Promise.resolve().then(() => (init_storage(), storage_exports));
    const user = await storage2.getUserById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        error: "Access denied",
        message: "User not found"
      });
    }
    const newToken = AuthService.generateToken({
      id: user.id,
      email: user.email,
      name: user.name
    });
    console.log(`\u{1F504} Token refreshed for user: ${user.email}`);
    res.json({
      success: true,
      message: "Token refreshed successfully",
      data: {
        token: newToken
      }
    });
  } catch (error) {
    console.error("\u274C Token refresh error:", error);
    if (error instanceof Error) {
      if (error.message === "Token too old for refresh") {
        return res.status(401).json({
          error: "Token expired",
          message: "Token is too old to refresh, please login again"
        });
      } else if (error.message === "Invalid token") {
        return res.status(401).json({
          error: "Invalid token",
          message: "Authentication token is invalid"
        });
      }
    }
    res.status(500).json({
      error: "Token refresh failed",
      message: "An unexpected error occurred"
    });
  }
});
var auth_default = router;

// server/index.ts
config2();
console.log("\u{1F527} Validating environment configuration...");
if (!process.env.GOOGLE_CLIENT_ID) {
  console.error("\u274C GOOGLE_CLIENT_ID environment variable not found");
  process.exit(1);
}
if (!process.env.GOOGLE_CLIENT_SECRET) {
  console.error("\u274C GOOGLE_CLIENT_SECRET environment variable not found");
  process.exit(1);
}
if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
  console.error("\u274C JWT_SECRET environment variable not found or too short (minimum 32 characters)");
  console.error("\u274C Generate using: openssl rand -base64 32");
  process.exit(1);
}
if (!process.env.ENCRYPTION_KEY || process.env.ENCRYPTION_KEY.length < 32) {
  console.error("\u274C ENCRYPTION_KEY environment variable not found or too short (minimum 32 characters)");
  console.error("\u274C Generate using: openssl rand -base64 32");
  process.exit(1);
}
console.log("\u2705 Environment configuration validated");
var app = express2();
app.use(securityHeaders);
app.use(corsConfig);
app.use(requestLogger);
app.use(generalRateLimit);
app.use(requestSizeLimit("10mb"));
app.use(express2.json({ limit: "10mb" }));
app.use(express2.urlencoded({ extended: false, limit: "10mb" }));
app.set("trust proxy", 1);
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  app.use("/api/auth", auth_default);
  const server = await registerRoutes(app);
  app.use(securityErrorHandler);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    console.error("\u274C Unhandled error:", err);
    if (process.env.NODE_ENV === "production") {
      res.status(status).json({
        error: "Internal server error",
        message: status === 500 ? "An unexpected error occurred" : message
      });
    } else {
      res.status(status).json({ message });
    }
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = parseInt(process.env.PORT || "8080", 10);
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();
