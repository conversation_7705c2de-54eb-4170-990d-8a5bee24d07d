import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import StatisticsCards from '../../../client/src/components/statistics-cards';

// Mock the API hook
jest.mock('../../../client/src/hooks/use-reviews', () => ({
  useReviews: () => ({
    data: [
      {
        id: 1,
        rating: 5,
        comment: 'Great hotel!',
        reviewerName: 'John Doe',
        sentiment: 'positive',
        responseComment: null
      },
      {
        id: 2,
        rating: 3,
        comment: 'Average experience',
        reviewerName: '<PERSON>',
        sentiment: 'neutral',
        responseComment: 'Thank you for feedback'
      }
    ],
    isLoading: false,
    error: null
  })
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('StatisticsCards', () => {
  it('should render statistics cards', () => {
    renderWithQueryClient(<StatisticsCards />);
    
    // Check for key statistics
    expect(screen.getByText('Total Reviews')).toBeInTheDocument();
    expect(screen.getByText('Average Rating')).toBeInTheDocument();
    expect(screen.getByText('Response Rate')).toBeInTheDocument();
  });

  it('should display correct total reviews count', () => {
    renderWithQueryClient(<StatisticsCards />);
    
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('should calculate and display average rating', () => {
    renderWithQueryClient(<StatisticsCards />);
    
    // Average of 5 and 3 should be 4.0
    expect(screen.getByText('4.0')).toBeInTheDocument();
  });

  it('should calculate and display response rate', () => {
    renderWithQueryClient(<StatisticsCards />);
    
    // 1 out of 2 reviews has a response = 50%
    expect(screen.getByText('50%')).toBeInTheDocument();
  });

  it('should handle loading state', () => {
    // Mock loading state
    jest.doMock('../../../client/src/hooks/use-reviews', () => ({
      useReviews: () => ({
        data: undefined,
        isLoading: true,
        error: null
      })
    }));

    renderWithQueryClient(<StatisticsCards />);
    
    // Should show loading indicators or skeleton
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should handle error state', () => {
    // Mock error state
    jest.doMock('../../../client/src/hooks/use-reviews', () => ({
      useReviews: () => ({
        data: undefined,
        isLoading: false,
        error: new Error('Failed to fetch reviews')
      })
    }));

    renderWithQueryClient(<StatisticsCards />);
    
    // Should show error message
    expect(screen.getByText(/error/i)).toBeInTheDocument();
  });

  it('should handle empty reviews', () => {
    // Mock empty reviews
    jest.doMock('../../../client/src/hooks/use-reviews', () => ({
      useReviews: () => ({
        data: [],
        isLoading: false,
        error: null
      })
    }));

    renderWithQueryClient(<StatisticsCards />);
    
    expect(screen.getByText('0')).toBeInTheDocument(); // Total reviews
    expect(screen.getByText('0.0')).toBeInTheDocument(); // Average rating
    expect(screen.getByText('0%')).toBeInTheDocument(); // Response rate
  });

  it('should display sentiment distribution', () => {
    renderWithQueryClient(<StatisticsCards />);
    
    // Should show positive and neutral sentiment indicators
    expect(screen.getByText(/positive/i)).toBeInTheDocument();
    expect(screen.getByText(/neutral/i)).toBeInTheDocument();
  });

  it('should be accessible', () => {
    renderWithQueryClient(<StatisticsCards />);
    
    // Check for proper ARIA labels and roles
    const cards = screen.getAllByRole('article');
    expect(cards.length).toBeGreaterThan(0);
    
    // Each card should have accessible content
    cards.forEach(card => {
      expect(card).toBeVisible();
    });
  });

  it('should format numbers correctly', () => {
    renderWithQueryClient(<StatisticsCards />);
    
    // Check that numbers are formatted properly
    const averageRating = screen.getByText('4.0');
    expect(averageRating).toBeInTheDocument();
    
    const responseRate = screen.getByText('50%');
    expect(responseRate).toBeInTheDocument();
  });

  it('should update when data changes', () => {
    const { rerender } = renderWithQueryClient(<StatisticsCards />);
    
    // Initial state
    expect(screen.getByText('2')).toBeInTheDocument();
    
    // Mock updated data
    jest.doMock('../../../client/src/hooks/use-reviews', () => ({
      useReviews: () => ({
        data: [
          {
            id: 1,
            rating: 5,
            comment: 'Great hotel!',
            reviewerName: 'John Doe',
            sentiment: 'positive',
            responseComment: null
          },
          {
            id: 2,
            rating: 3,
            comment: 'Average experience',
            reviewerName: 'Jane Smith',
            sentiment: 'neutral',
            responseComment: 'Thank you for feedback'
          },
          {
            id: 3,
            rating: 4,
            comment: 'Good service',
            reviewerName: 'Bob Johnson',
            sentiment: 'positive',
            responseComment: null
          }
        ],
        isLoading: false,
        error: null
      })
    }));
    
    rerender(
      <QueryClientProvider client={createTestQueryClient()}>
        <StatisticsCards />
      </QueryClientProvider>
    );
    
    // Should show updated count
    expect(screen.getByText('3')).toBeInTheDocument();
  });
});
