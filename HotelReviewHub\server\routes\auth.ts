import { Router, Request, Response } from 'express';
import { AuthService } from '../services/auth';
import { validateRegister, validateLogin } from '../middleware/validation';
import { authRateLimit } from '../middleware/security';

const router = Router();

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post('/register', authRateLimit, validateRegister, async (req: Request, res: Response) => {
  try {
    const { email, password, name } = req.body;

    console.log(`📝 Registration attempt for email: ${email}`);

    // Register user
    const user = await AuthService.registerUser(email, password, name);
    
    // Generate token
    const token = AuthService.generateToken(user);

    console.log(`✅ User registered successfully: ${email}`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        },
        token
      }
    });

  } catch (error) {
    console.error('❌ Registration error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'User already exists with this email') {
        return res.status(409).json({
          error: 'Registration failed',
          message: 'An account with this email already exists'
        });
      }
    }

    res.status(500).json({
      error: 'Registration failed',
      message: 'An unexpected error occurred during registration'
    });
  }
});

/**
 * POST /api/auth/login
 * Login a user
 */
router.post('/login', authRateLimit, validateLogin, async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    console.log(`🔐 Login attempt for email: ${email}`);

    // Login user
    const { user, token } = await AuthService.loginUser(email, password);

    console.log(`✅ User logged in successfully: ${email}`);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        },
        token
      }
    });

  } catch (error) {
    console.error('❌ Login error:', error);
    
    if (error instanceof Error && error.message === 'Invalid email or password') {
      return res.status(401).json({
        error: 'Login failed',
        message: 'Invalid email or password'
      });
    }

    res.status(500).json({
      error: 'Login failed',
      message: 'An unexpected error occurred during login'
    });
  }
});

/**
 * POST /api/auth/logout
 * Logout a user (client-side token removal)
 */
router.post('/logout', (req: Request, res: Response) => {
  // Since we're using stateless JWT tokens, logout is handled client-side
  // by removing the token from storage. We just acknowledge the logout.
  
  console.log('👋 User logout request');
  
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

/**
 * GET /api/auth/me
 * Get current user information
 */
router.get('/me', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Authentication token required'
      });
    }

    // Verify token
    const decoded = AuthService.verifyToken(token);
    
    // Get user from database
    const { storage } = await import('../storage');
    const user = await storage.getUserById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        }
      }
    });

  } catch (error) {
    console.error('❌ Get user error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Token expired') {
        return res.status(401).json({
          error: 'Token expired',
          message: 'Authentication token has expired'
        });
      } else if (error.message === 'Invalid token') {
        return res.status(401).json({
          error: 'Invalid token',
          message: 'Authentication token is invalid'
        });
      }
    }

    res.status(500).json({
      error: 'Authentication failed',
      message: 'An unexpected error occurred'
    });
  }
});

/**
 * POST /api/auth/refresh
 * Refresh JWT token (optional - for extended sessions)
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Authentication token required'
      });
    }

    // Verify token (even if expired, we can still decode it)
    let decoded;
    try {
      decoded = AuthService.verifyToken(token);
    } catch (error) {
      if (error instanceof Error && error.message === 'Token expired') {
        // For refresh, we allow expired tokens but verify they're not too old
        const jwt = require('jsonwebtoken');
        decoded = jwt.decode(token) as any;
        
        if (!decoded || !decoded.exp) {
          throw new Error('Invalid token');
        }
        
        // Check if token is not older than 7 days
        const tokenAge = Date.now() / 1000 - decoded.exp;
        if (tokenAge > 7 * 24 * 60 * 60) { // 7 days in seconds
          throw new Error('Token too old for refresh');
        }
      } else {
        throw error;
      }
    }

    // Get user from database
    const { storage } = await import('../storage');
    const user = await storage.getUserById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'User not found'
      });
    }

    // Generate new token
    const newToken = AuthService.generateToken({
      id: user.id,
      email: user.email,
      name: user.name
    });

    console.log(`🔄 Token refreshed for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newToken
      }
    });

  } catch (error) {
    console.error('❌ Token refresh error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Token too old for refresh') {
        return res.status(401).json({
          error: 'Token expired',
          message: 'Token is too old to refresh, please login again'
        });
      } else if (error.message === 'Invalid token') {
        return res.status(401).json({
          error: 'Invalid token',
          message: 'Authentication token is invalid'
        });
      }
    }

    res.status(500).json({
      error: 'Token refresh failed',
      message: 'An unexpected error occurred'
    });
  }
});

export default router;
