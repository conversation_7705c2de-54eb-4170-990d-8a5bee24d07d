import { useQuery } from "@tanstack/react-query";
import type { BusinessInfo } from "@shared/schema";

interface BusinessData {
  businessInfo: BusinessInfo | null;
  isSetup: boolean;
  setupUrl: string | null;
}

interface SetupData {
  isSetup: boolean;
  setupUrl: string | null;
}

export function useBusinessInfo() {
  return useQuery<BusinessData>({
    queryKey: ['/api/business'],
    refetchInterval: 30000, // Refetch every 30 seconds
  });
}

// Hook for initial setup flow (no authentication required)
export function useSetupInfo() {
  return useQuery<SetupData>({
    queryKey: ['/api/setup'],
    refetchInterval: 10000, // Refetch every 10 seconds during setup
  });
}
