import helmet from 'helmet';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';

/**
 * Security headers middleware using Helmet
 */
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://mybusinessbusinessinformation.googleapis.com", "https://mybusiness.googleapis.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
    },
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
});

/**
 * CORS configuration
 */
export const corsConfig = cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'http://localhost:8081',
      'http://localhost:3000',
      'http://127.0.0.1:8081',
      'http://127.0.0.1:3000'
    ];

    // Add production domains if configured
    if (process.env.ALLOWED_ORIGINS) {
      allowedOrigins.push(...process.env.ALLOWED_ORIGINS.split(','));
    }

    // Add Replit domains if configured
    if (process.env.REPLIT_DOMAINS) {
      const replitDomains = process.env.REPLIT_DOMAINS.split(',');
      replitDomains.forEach(domain => {
        allowedOrigins.push(`https://${domain}`);
      });
    }

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`❌ CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-CSRF-Token'
  ],
  exposedHeaders: ['X-CSRF-Token'],
  maxAge: 86400 // 24 hours
});

/**
 * General API rate limiting
 */
export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests',
    message: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`❌ Rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: 'Too many requests',
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: '15 minutes'
    });
  }
});

/**
 * Strict rate limiting for authentication endpoints
 */
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 auth requests per windowMs
  message: {
    error: 'Too many authentication attempts',
    message: 'Too many authentication attempts from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  handler: (req, res) => {
    console.warn(`❌ Auth rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: 'Too many authentication attempts',
      message: 'Too many authentication attempts from this IP, please try again later.',
      retryAfter: '15 minutes'
    });
  }
});

/**
 * API-specific rate limiting for Google API calls
 */
export const apiRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // Limit each IP to 30 API requests per minute
  message: {
    error: 'API rate limit exceeded',
    message: 'Too many API requests from this IP, please try again later.',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`❌ API rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: 'API rate limit exceeded',
      message: 'Too many API requests from this IP, please try again later.',
      retryAfter: '1 minute'
    });
  }
});

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  const { method, url, ip } = req;
  const userAgent = req.get('User-Agent') || 'Unknown';

  // Log request
  console.log(`📥 ${method} ${url} - IP: ${ip} - UA: ${userAgent.substring(0, 50)}`);

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    const { statusCode } = res;
    const statusEmoji = statusCode >= 400 ? '❌' : statusCode >= 300 ? '⚠️' : '✅';
    
    console.log(`📤 ${statusEmoji} ${method} ${url} - ${statusCode} - ${duration}ms - IP: ${ip}`);
    
    // Log security events
    if (statusCode === 401) {
      console.warn(`🔒 Unauthorized access attempt: ${method} ${url} - IP: ${ip}`);
    } else if (statusCode === 403) {
      console.warn(`🚫 Forbidden access attempt: ${method} ${url} - IP: ${ip}`);
    } else if (statusCode === 429) {
      console.warn(`⏰ Rate limit hit: ${method} ${url} - IP: ${ip}`);
    }
  });

  next();
};

/**
 * Error handling middleware for security-related errors
 */
export const securityErrorHandler = (error: any, req: Request, res: Response, next: NextFunction) => {
  // CORS errors
  if (error.message === 'Not allowed by CORS') {
    console.error(`❌ CORS error for ${req.method} ${req.url} from origin: ${req.get('Origin')}`);
    return res.status(403).json({
      error: 'CORS policy violation',
      message: 'Origin not allowed by CORS policy'
    });
  }

  // Rate limiting errors
  if (error.type === 'entity.too.large') {
    console.error(`❌ Request too large: ${req.method} ${req.url} - IP: ${req.ip}`);
    return res.status(413).json({
      error: 'Request too large',
      message: 'Request entity too large'
    });
  }

  // Validation errors
  if (error.type === 'entity.parse.failed') {
    console.error(`❌ JSON parse error: ${req.method} ${req.url} - IP: ${req.ip}`);
    return res.status(400).json({
      error: 'Invalid JSON',
      message: 'Request body contains invalid JSON'
    });
  }

  // Default security error
  console.error(`❌ Security error: ${error.message} - ${req.method} ${req.url} - IP: ${req.ip}`);
  
  // Don't expose internal error details in production
  if (process.env.NODE_ENV === 'production') {
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred'
    });
  }

  next(error);
};

/**
 * IP whitelist middleware (for admin endpoints if needed)
 */
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    
    if (!allowedIPs.includes(clientIP) && !allowedIPs.includes('127.0.0.1') && !allowedIPs.includes('::1')) {
      console.warn(`❌ IP not whitelisted: ${clientIP} attempted ${req.method} ${req.url}`);
      return res.status(403).json({
        error: 'Access denied',
        message: 'IP address not authorized'
      });
    }
    
    next();
  };
};

/**
 * Request size limiting middleware
 */
export const requestSizeLimit = (maxSize: string = '10mb') => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.get('Content-Length');
    const maxBytes = parseSize(maxSize);
    
    if (contentLength && parseInt(contentLength) > maxBytes) {
      console.warn(`❌ Request too large: ${contentLength} bytes from IP: ${req.ip}`);
      return res.status(413).json({
        error: 'Request too large',
        message: `Request size exceeds ${maxSize} limit`
      });
    }
    
    next();
  };
};

/**
 * Helper function to parse size strings like '10mb' to bytes
 */
function parseSize(size: string): number {
  const units: { [key: string]: number } = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)(b|kb|mb|gb)$/);
  if (!match) return 1024 * 1024; // Default 1MB
  
  const [, num, unit] = match;
  return Math.floor(parseFloat(num) * units[unit]);
}
