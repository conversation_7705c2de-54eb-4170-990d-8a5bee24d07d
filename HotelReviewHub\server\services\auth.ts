import { config } from 'dotenv';
config(); // Load environment variables

import * as jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { Request, Response, NextFunction } from 'express';
import { storage } from '../storage';

// Environment validation
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

if (!JWT_SECRET || JWT_SECRET.length < 32) {
  if (process.env.NODE_ENV !== 'test') {
    console.error('❌ CRITICAL SECURITY ERROR: JWT_SECRET not found or too short in environment variables');
    console.error('❌ Required: JWT_SECRET with minimum 32 characters');
    console.error('❌ Generate using: openssl rand -base64 32');
    throw new Error('Missing or invalid JWT_SECRET in environment variables');
  }
}

export interface AuthUser {
  id: number;
  email: string;
  name: string;
}

export interface JWTPayload {
  userId: number;
  email: string;
  iat?: number;
  exp?: number;
}

export class AuthService {
  /**
   * Hash a password using bcrypt
   */
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12; // High security salt rounds
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Verify a password against its hash
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Generate a JWT token for a user
   */
  static generateToken(user: AuthUser): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email
    };

    const secret = JWT_SECRET || 'test-secret-for-testing-only';
    return jwt.sign(payload, secret as string, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'HotelReviewHub',
      audience: 'HotelReviewHub-Users'
    });
  }

  /**
   * Verify and decode a JWT token
   */
  static verifyToken(token: string): JWTPayload {
    try {
      const secret = JWT_SECRET || 'test-secret-for-testing-only';
      return jwt.verify(token, secret as string, {
        issuer: 'HotelReviewHub',
        audience: 'HotelReviewHub-Users'
      }) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else {
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Register a new user
   */
  static async registerUser(email: string, password: string, name: string): Promise<AuthUser> {
    // Check if user already exists
    const existingUser = await storage.getUserByEmail(email);
    if (existingUser) {
      throw new Error('User already exists with this email');
    }

    // Hash password
    const hashedPassword = await this.hashPassword(password);

    // Create user
    const userId = await storage.createUser({
      username: email, // Use email as username for now
      email,
      password: hashedPassword,
      name
    });

    return {
      id: userId,
      email,
      name
    };
  }

  /**
   * Login a user
   */
  static async loginUser(email: string, password: string): Promise<{ user: AuthUser; token: string }> {
    // Get user by email
    const user = await storage.getUserByEmail(email);
    if (!user) {
      throw new Error('Invalid email or password');
    }

    // Verify password
    const isValidPassword = await this.verifyPassword(password, user.password);
    if (!isValidPassword) {
      throw new Error('Invalid email or password');
    }

    // Generate token
    const authUser: AuthUser = {
      id: user.id,
      email: user.email,
      name: user.name
    };

    const token = this.generateToken(authUser);

    return {
      user: authUser,
      token
    };
  }
}

/**
 * Express middleware to authenticate requests
 */
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Authentication token required'
      });
    }

    // Verify token
    const decoded = AuthService.verifyToken(token);

    // Get user from database to ensure they still exist
    const user = await storage.getUserById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'User not found'
      });
    }

    // Add user to request object
    (req as any).user = {
      id: user.id,
      email: user.email,
      name: user.name
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({
      error: 'Access denied',
      message: error instanceof Error ? error.message : 'Invalid token'
    });
  }
};

/**
 * Express middleware for optional authentication
 * Sets req.user if token is valid, but doesn't block request if no token
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = AuthService.verifyToken(token);
      const user = await storage.getUserById(decoded.userId);
      
      if (user) {
        (req as any).user = {
          id: user.id,
          email: user.email,
          name: user.name
        };
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't block the request on auth errors
    console.warn('Optional authentication failed:', error);
    next();
  }
};
