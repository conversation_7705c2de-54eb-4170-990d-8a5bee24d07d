# HotelReviewHub - Production Deployment Guide

## 🚀 Quick Deployment

### Prerequisites
- Node.js 20+
- Google Cloud Console project with Business Profile API enabled
- Google Business Profile with management permissions

### Environment Setup
```bash
# Required Environment Variables
NODE_ENV=production
PORT=8081
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:8081/api/auth/google/callback
DATABASE_URL=./database.sqlite
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
ENCRYPTION_KEY=your_encryption_key_here_minimum_32_characters
```

### Production Build & Start
```bash
# 1. Install dependencies
npm install

# 2. Run tests (optional but recommended)
npm test

# 3. Build for production
npm run build

# 4. Start production server
npm start
```

## 🔧 Google Cloud Console Configuration

### Required APIs
Enable these APIs in your Google Cloud Console:
- Google Business Profile API (Account Management v1)
- Google Business Profile API (Business Information v1)
- Google Business Profile API (Reviews v1)

### OAuth Configuration
1. Go to Google Cloud Console → APIs & Services → Credentials
2. Create or find your OAuth Client ID
3. Configure:
   - **Authorized JavaScript origins**: `http://localhost:8081`
   - **Authorized redirect URIs**: `http://localhost:8081/api/auth/google/callback`

## 📊 Production Features

### ✅ What's Included
- **Automated Review Sync**: Every 30 minutes with error recovery
- **Intelligent Quota Management**: Handles Google API limits gracefully
- **Professional Dashboard**: Real-time analytics and review management
- **Direct Response System**: Reply to Google reviews directly
- **Advanced Sentiment Analysis**: AI-powered review categorization
- **Production Database**: SQLite with WAL mode and optimizations
- **Database Encryption**: AES-256-GCM encryption for sensitive data
- **Comprehensive Logging**: Detailed monitoring and troubleshooting
- **Testing Framework**: Jest-based testing infrastructure for reliability

### 🛡️ Security Features
- Secure OAuth token management with automatic refresh
- AES-256-GCM encryption for refresh tokens and business data
- Environment variable protection
- Input validation and sanitization
- Error boundary protection

## 🔍 Monitoring & Troubleshooting

### Health Checks
- Application: `http://localhost:8081/api/business`
- Database: SQLite file at `./database.sqlite`
- Logs: Check console output for detailed operation logs

### Common Issues
- **Quota Exceeded**: Normal behavior, system handles automatically
- **Authentication Issues**: Verify Google Cloud Console configuration
- **Database Issues**: Check SQLite file permissions and disk space

## 📈 Performance Metrics

### Expected Performance
- **Page Load**: <2 seconds
- **API Response**: <500ms average
- **Review Sync**: 30-minute intervals
- **API Usage**: ~50-100 calls per day for normal operations

### Optimization Features
- React Query caching for reduced API calls
- Efficient database queries with proper indexing
- Background processing for non-blocking operations
- Intelligent retry mechanisms for reliability

---

## 🚀 Development Status

### ✅ Phase 1: Security Implementation - COMPLETE
- **Database Encryption**: AES-256-GCM encryption for sensitive data
- **Secure Token Management**: Encrypted refresh token storage
- **Input Validation**: Comprehensive data sanitization
- **Authentication Security**: OAuth 2.0 with secure token handling

### 🔄 Phase 2: Core Stability - IN PROGRESS
- **✅ Testing Framework**: Jest configuration resolved and functional
- **🔄 Test Suite Development**: Implementing comprehensive unit and integration tests
- **📋 Monitoring System**: Real-time application monitoring (planned)
- **📋 Automated Backups**: Database backup automation (planned)
- **📋 Health Checks**: System health monitoring endpoints (planned)

### 📋 Phase 3: UI/UX Refinement - PLANNED
- Mobile-responsive design improvements
- Modern UI component updates
- Professional styling enhancements
- User experience optimization

## 🧪 Testing Commands

```bash
# Run all tests
npm test

# Run tests in watch mode (development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

## 🎯 Production Ready

The HotelReviewHub system is **production-ready** with all core features implemented and Phase 1 security measures complete. Phase 2 stability improvements are in progress to enhance system reliability and monitoring.

**Ready to manage your hotel reviews professionally!** 🏨⭐
