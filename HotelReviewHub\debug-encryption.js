#!/usr/bin/env node

import dotenv from 'dotenv';
dotenv.config();

console.log('🔍 Debugging Encryption Configuration');
console.log('ENCRYPTION_KEY exists:', !!process.env.ENCRYPTION_KEY);
console.log('ENCRYPTION_KEY length:', process.env.ENCRYPTION_KEY?.length || 0);
console.log('ENCRYPTION_KEY value (first 10 chars):', process.env.ENCRYPTION_KEY?.substring(0, 10) || 'undefined');

import crypto from 'crypto';

// Test the validation logic directly
function testValidation() {
  const key = process.env.ENCRYPTION_KEY;

  console.log('\n🧪 Testing validation logic:');

  if (!key) {
    console.log('❌ Key is not set');
    return false;
  }

  console.log('✅ Key is set');

  if (key.length < 32) {
    console.log(`❌ Key is too short: ${key.length} characters (need 32+)`);
    return false;
  }

  console.log(`✅ Key length is sufficient: ${key.length} characters`);

  // Test crypto hash creation
  try {
    const hashedKey = crypto.createHash('sha256').update(key).digest();
    console.log('✅ Key can be hashed successfully');
    console.log('Hashed key length:', hashedKey.length);
    return true;
  } catch (error) {
    console.log('❌ Error hashing key:', error.message);
    return false;
  }
}

const result = testValidation();
console.log('\n📊 Final result:', result ? '✅ PASS' : '❌ FAIL');
