import { config } from "dotenv";
config();

import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

// Import security middleware
import {
  security<PERSON><PERSON><PERSON>,
  corsConfig,
  generalRateLimit,
  request<PERSON>ogger,
  securityErrorHandler,
  requestSizeLimit
} from "./middleware/security";

// Import authentication routes
import authRoutes from "./routes/auth";

// Validate critical environment variables
console.log('🔧 Validating environment configuration...');

// Google OAuth credentials
if (!process.env.GOOGLE_CLIENT_ID) {
  console.error('❌ GOOGLE_CLIENT_ID environment variable not found');
  process.exit(1);
}
if (!process.env.GOOGLE_CLIENT_SECRET) {
  console.error('❌ GOOGLE_CLIENT_SECRET environment variable not found');
  process.exit(1);
}

// Security credentials
if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
  console.error('❌ JWT_SECRET environment variable not found or too short (minimum 32 characters)');
  console.error('❌ Generate using: openssl rand -base64 32');
  process.exit(1);
}

if (!process.env.ENCRYPTION_KEY || process.env.ENCRYPTION_KEY.length < 32) {
  console.error('❌ ENCRYPTION_KEY environment variable not found or too short (minimum 32 characters)');
  console.error('❌ Generate using: openssl rand -base64 32');
  process.exit(1);
}

console.log('✅ Environment configuration validated');

const app = express();

// ===== SECURITY MIDDLEWARE =====
// Apply security headers first
app.use(securityHeaders);

// CORS configuration
app.use(corsConfig);

// Request logging
app.use(requestLogger);

// Rate limiting
app.use(generalRateLimit);

// Request size limiting
app.use(requestSizeLimit('10mb'));

// Body parsing with security
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

// Trust proxy for accurate IP addresses (important for rate limiting)
app.set('trust proxy', 1);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // ===== AUTHENTICATION ROUTES =====
  // Mount authentication routes before other routes
  app.use('/api/auth', authRoutes);

  // ===== MAIN APPLICATION ROUTES =====
  const server = await registerRoutes(app);

  // ===== ERROR HANDLING =====
  // Security error handler
  app.use(securityErrorHandler);

  // General error handler
  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    console.error('❌ Unhandled error:', err);

    // Don't expose internal errors in production
    if (process.env.NODE_ENV === 'production') {
      res.status(status).json({
        error: 'Internal server error',
        message: status === 500 ? 'An unexpected error occurred' : message
      });
    } else {
      res.status(status).json({ message });
    }
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Use PORT from environment variables or default to 8080
  // this serves both the API and the client.
  const port = parseInt(process.env.PORT || "8080", 10);
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();
