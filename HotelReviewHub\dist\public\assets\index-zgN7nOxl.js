var Kd=e=>{throw TypeError(e)};var dl=(e,t,n)=>t.has(e)||Kd("Cannot "+n);var E=(e,t,n)=>(dl(e,t,"read from private field"),n?n.call(e):t.get(e)),W=(e,t,n)=>t.has(e)?Kd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),I=(e,t,n,r)=>(dl(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),G=(e,t,n)=>(dl(e,t,"access private method"),n);var oi=(e,t,n,r)=>({set _(o){I(e,t,o,n)},get _(){return E(e,t,r)}});function Y0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function hp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var pp={exports:{}},Aa={},mp={exports:{}},K={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vs=Symbol.for("react.element"),K0=Symbol.for("react.portal"),G0=Symbol.for("react.fragment"),q0=Symbol.for("react.strict_mode"),X0=Symbol.for("react.profiler"),Z0=Symbol.for("react.provider"),J0=Symbol.for("react.context"),ex=Symbol.for("react.forward_ref"),tx=Symbol.for("react.suspense"),nx=Symbol.for("react.memo"),rx=Symbol.for("react.lazy"),Gd=Symbol.iterator;function ox(e){return e===null||typeof e!="object"?null:(e=Gd&&e[Gd]||e["@@iterator"],typeof e=="function"?e:null)}var gp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},vp=Object.assign,yp={};function ko(e,t,n){this.props=e,this.context=t,this.refs=yp,this.updater=n||gp}ko.prototype.isReactComponent={};ko.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ko.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function xp(){}xp.prototype=ko.prototype;function Pc(e,t,n){this.props=e,this.context=t,this.refs=yp,this.updater=n||gp}var kc=Pc.prototype=new xp;kc.constructor=Pc;vp(kc,ko.prototype);kc.isPureReactComponent=!0;var qd=Array.isArray,wp=Object.prototype.hasOwnProperty,Tc={current:null},Sp={key:!0,ref:!0,__self:!0,__source:!0};function Cp(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)wp.call(t,r)&&!Sp.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),c=0;c<a;c++)l[c]=arguments[c+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Vs,type:e,key:s,ref:i,props:o,_owner:Tc.current}}function sx(e,t){return{$$typeof:Vs,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Rc(e){return typeof e=="object"&&e!==null&&e.$$typeof===Vs}function ix(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Xd=/\/+/g;function fl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?ix(""+e.key):t.toString(36)}function Mi(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Vs:case K0:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+fl(i,0):r,qd(o)?(n="",e!=null&&(n=e.replace(Xd,"$&/")+"/"),Mi(o,t,n,"",function(c){return c})):o!=null&&(Rc(o)&&(o=sx(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(Xd,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",qd(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+fl(s,a);i+=Mi(s,t,n,l,o)}else if(l=ox(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+fl(s,a++),i+=Mi(s,t,n,l,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function si(e,t,n){if(e==null)return e;var r=[],o=0;return Mi(e,r,"","",function(s){return t.call(n,s,o++)}),r}function ax(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ke={current:null},_i={transition:null},lx={ReactCurrentDispatcher:Ke,ReactCurrentBatchConfig:_i,ReactCurrentOwner:Tc};function Ep(){throw Error("act(...) is not supported in production builds of React.")}K.Children={map:si,forEach:function(e,t,n){si(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return si(e,function(){t++}),t},toArray:function(e){return si(e,function(t){return t})||[]},only:function(e){if(!Rc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};K.Component=ko;K.Fragment=G0;K.Profiler=X0;K.PureComponent=Pc;K.StrictMode=q0;K.Suspense=tx;K.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=lx;K.act=Ep;K.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=vp({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=Tc.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)wp.call(t,l)&&!Sp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var c=0;c<l;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:Vs,type:e.type,key:o,ref:s,props:r,_owner:i}};K.createContext=function(e){return e={$$typeof:J0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Z0,_context:e},e.Consumer=e};K.createElement=Cp;K.createFactory=function(e){var t=Cp.bind(null,e);return t.type=e,t};K.createRef=function(){return{current:null}};K.forwardRef=function(e){return{$$typeof:ex,render:e}};K.isValidElement=Rc;K.lazy=function(e){return{$$typeof:rx,_payload:{_status:-1,_result:e},_init:ax}};K.memo=function(e,t){return{$$typeof:nx,type:e,compare:t===void 0?null:t}};K.startTransition=function(e){var t=_i.transition;_i.transition={};try{e()}finally{_i.transition=t}};K.unstable_act=Ep;K.useCallback=function(e,t){return Ke.current.useCallback(e,t)};K.useContext=function(e){return Ke.current.useContext(e)};K.useDebugValue=function(){};K.useDeferredValue=function(e){return Ke.current.useDeferredValue(e)};K.useEffect=function(e,t){return Ke.current.useEffect(e,t)};K.useId=function(){return Ke.current.useId()};K.useImperativeHandle=function(e,t,n){return Ke.current.useImperativeHandle(e,t,n)};K.useInsertionEffect=function(e,t){return Ke.current.useInsertionEffect(e,t)};K.useLayoutEffect=function(e,t){return Ke.current.useLayoutEffect(e,t)};K.useMemo=function(e,t){return Ke.current.useMemo(e,t)};K.useReducer=function(e,t,n){return Ke.current.useReducer(e,t,n)};K.useRef=function(e){return Ke.current.useRef(e)};K.useState=function(e){return Ke.current.useState(e)};K.useSyncExternalStore=function(e,t,n){return Ke.current.useSyncExternalStore(e,t,n)};K.useTransition=function(){return Ke.current.useTransition()};K.version="18.3.1";mp.exports=K;var p=mp.exports;const mn=hp(p),bp=Y0({__proto__:null,default:mn},[p]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ux=p,cx=Symbol.for("react.element"),dx=Symbol.for("react.fragment"),fx=Object.prototype.hasOwnProperty,hx=ux.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,px={key:!0,ref:!0,__self:!0,__source:!0};function Np(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)fx.call(t,r)&&!px.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:cx,type:e,key:s,ref:i,props:o,_owner:hx.current}}Aa.Fragment=dx;Aa.jsx=Np;Aa.jsxs=Np;pp.exports=Aa;var u=pp.exports,Pp={exports:{}},dt={},kp={exports:{}},Tp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,M){var L=T.length;T.push(M);e:for(;0<L;){var U=L-1>>>1,re=T[U];if(0<o(re,M))T[U]=M,T[L]=re,L=U;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var M=T[0],L=T.pop();if(L!==M){T[0]=L;e:for(var U=0,re=T.length,Ue=re>>>1;U<Ue;){var Ee=2*(U+1)-1,Dt=T[Ee],Be=Ee+1,$=T[Be];if(0>o(Dt,L))Be<re&&0>o($,Dt)?(T[U]=$,T[Be]=L,U=Be):(T[U]=Dt,T[Ee]=L,U=Ee);else if(Be<re&&0>o($,L))T[U]=$,T[Be]=L,U=Be;else break e}}return M}function o(T,M){var L=T.sortIndex-M.sortIndex;return L!==0?L:T.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var l=[],c=[],d=1,f=null,h=3,x=!1,S=!1,m=!1,w=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(T){for(var M=n(c);M!==null;){if(M.callback===null)r(c);else if(M.startTime<=T)r(c),M.sortIndex=M.expirationTime,t(l,M);else break;M=n(c)}}function C(T){if(m=!1,y(T),!S)if(n(l)!==null)S=!0,z(b);else{var M=n(c);M!==null&&V(C,M.startTime-T)}}function b(T,M){S=!1,m&&(m=!1,v(k),k=-1),x=!0;var L=h;try{for(y(M),f=n(l);f!==null&&(!(f.expirationTime>M)||T&&!F());){var U=f.callback;if(typeof U=="function"){f.callback=null,h=f.priorityLevel;var re=U(f.expirationTime<=M);M=e.unstable_now(),typeof re=="function"?f.callback=re:f===n(l)&&r(l),y(M)}else r(l);f=n(l)}if(f!==null)var Ue=!0;else{var Ee=n(c);Ee!==null&&V(C,Ee.startTime-M),Ue=!1}return Ue}finally{f=null,h=L,x=!1}}var P=!1,N=null,k=-1,j=5,O=-1;function F(){return!(e.unstable_now()-O<j)}function A(){if(N!==null){var T=e.unstable_now();O=T;var M=!0;try{M=N(!0,T)}finally{M?B():(P=!1,N=null)}}else P=!1}var B;if(typeof g=="function")B=function(){g(A)};else if(typeof MessageChannel<"u"){var _=new MessageChannel,Y=_.port2;_.port1.onmessage=A,B=function(){Y.postMessage(null)}}else B=function(){w(A,0)};function z(T){N=T,P||(P=!0,B())}function V(T,M){k=w(function(){T(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){S||x||(S=!0,z(b))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(T){switch(h){case 1:case 2:case 3:var M=3;break;default:M=h}var L=h;h=M;try{return T()}finally{h=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,M){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var L=h;h=T;try{return M()}finally{h=L}},e.unstable_scheduleCallback=function(T,M,L){var U=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?U+L:U):L=U,T){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=L+re,T={id:d++,callback:M,priorityLevel:T,startTime:L,expirationTime:re,sortIndex:-1},L>U?(T.sortIndex=L,t(c,T),n(l)===null&&T===n(c)&&(m?(v(k),k=-1):m=!0,V(C,L-U))):(T.sortIndex=re,t(l,T),S||x||(S=!0,z(b))),T},e.unstable_shouldYield=F,e.unstable_wrapCallback=function(T){var M=h;return function(){var L=h;h=M;try{return T.apply(this,arguments)}finally{h=L}}}})(Tp);kp.exports=Tp;var mx=kp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gx=p,ut=mx;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Rp=new Set,ps={};function Er(e,t){go(e,t),go(e+"Capture",t)}function go(e,t){for(ps[e]=t,e=0;e<t.length;e++)Rp.add(t[e])}var rn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ru=Object.prototype.hasOwnProperty,vx=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Zd={},Jd={};function yx(e){return ru.call(Jd,e)?!0:ru.call(Zd,e)?!1:vx.test(e)?Jd[e]=!0:(Zd[e]=!0,!1)}function xx(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function wx(e,t,n,r){if(t===null||typeof t>"u"||xx(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ge(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var _e={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){_e[e]=new Ge(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];_e[t]=new Ge(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){_e[e]=new Ge(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){_e[e]=new Ge(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){_e[e]=new Ge(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){_e[e]=new Ge(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){_e[e]=new Ge(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){_e[e]=new Ge(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){_e[e]=new Ge(e,5,!1,e.toLowerCase(),null,!1,!1)});var jc=/[\-:]([a-z])/g;function Oc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(jc,Oc);_e[t]=new Ge(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(jc,Oc);_e[t]=new Ge(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(jc,Oc);_e[t]=new Ge(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){_e[e]=new Ge(e,1,!1,e.toLowerCase(),null,!1,!1)});_e.xlinkHref=new Ge("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){_e[e]=new Ge(e,1,!1,e.toLowerCase(),null,!0,!0)});function Mc(e,t,n,r){var o=_e.hasOwnProperty(t)?_e[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(wx(t,n,o,r)&&(n=null),r||o===null?yx(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var cn=gx.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ii=Symbol.for("react.element"),Mr=Symbol.for("react.portal"),_r=Symbol.for("react.fragment"),_c=Symbol.for("react.strict_mode"),ou=Symbol.for("react.profiler"),jp=Symbol.for("react.provider"),Op=Symbol.for("react.context"),Ac=Symbol.for("react.forward_ref"),su=Symbol.for("react.suspense"),iu=Symbol.for("react.suspense_list"),Dc=Symbol.for("react.memo"),yn=Symbol.for("react.lazy"),Mp=Symbol.for("react.offscreen"),ef=Symbol.iterator;function $o(e){return e===null||typeof e!="object"?null:(e=ef&&e[ef]||e["@@iterator"],typeof e=="function"?e:null)}var ve=Object.assign,hl;function Zo(e){if(hl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);hl=t&&t[1]||""}return`
`+hl+e}var pl=!1;function ml(e,t){if(!e||pl)return"";pl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,a=s.length-1;1<=i&&0<=a&&o[i]!==s[a];)a--;for(;1<=i&&0<=a;i--,a--)if(o[i]!==s[a]){if(i!==1||a!==1)do if(i--,a--,0>a||o[i]!==s[a]){var l=`
`+o[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=i&&0<=a);break}}}finally{pl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Zo(e):""}function Sx(e){switch(e.tag){case 5:return Zo(e.type);case 16:return Zo("Lazy");case 13:return Zo("Suspense");case 19:return Zo("SuspenseList");case 0:case 2:case 15:return e=ml(e.type,!1),e;case 11:return e=ml(e.type.render,!1),e;case 1:return e=ml(e.type,!0),e;default:return""}}function au(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _r:return"Fragment";case Mr:return"Portal";case ou:return"Profiler";case _c:return"StrictMode";case su:return"Suspense";case iu:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Op:return(e.displayName||"Context")+".Consumer";case jp:return(e._context.displayName||"Context")+".Provider";case Ac:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Dc:return t=e.displayName||null,t!==null?t:au(e.type)||"Memo";case yn:t=e._payload,e=e._init;try{return au(e(t))}catch{}}return null}function Cx(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return au(t);case 8:return t===_c?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function _p(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ex(e){var t=_p(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ai(e){e._valueTracker||(e._valueTracker=Ex(e))}function Ap(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=_p(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Xi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function lu(e,t){var n=t.checked;return ve({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Dp(e,t){t=t.checked,t!=null&&Mc(e,"checked",t,!1)}function uu(e,t){Dp(e,t);var n=zn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?cu(e,t.type,n):t.hasOwnProperty("defaultValue")&&cu(e,t.type,zn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function cu(e,t,n){(t!=="number"||Xi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Jo=Array.isArray;function Vr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function du(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return ve({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function rf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(Jo(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zn(n)}}function Ip(e,t){var n=zn(t.value),r=zn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function of(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Lp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function fu(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Lp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var li,Fp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(li=li||document.createElement("div"),li.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ms(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var os={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},bx=["Webkit","ms","Moz","O"];Object.keys(os).forEach(function(e){bx.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),os[t]=os[e]})});function zp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||os.hasOwnProperty(e)&&os[e]?(""+t).trim():t+"px"}function $p(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=zp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Nx=ve({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function hu(e,t){if(t){if(Nx[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function pu(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var mu=null;function Ic(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var gu=null,Hr=null,Qr=null;function sf(e){if(e=Ys(e)){if(typeof gu!="function")throw Error(R(280));var t=e.stateNode;t&&(t=za(t),gu(e.stateNode,e.type,t))}}function Wp(e){Hr?Qr?Qr.push(e):Qr=[e]:Hr=e}function Up(){if(Hr){var e=Hr,t=Qr;if(Qr=Hr=null,sf(e),t)for(e=0;e<t.length;e++)sf(t[e])}}function Bp(e,t){return e(t)}function Vp(){}var gl=!1;function Hp(e,t,n){if(gl)return e(t,n);gl=!0;try{return Bp(e,t,n)}finally{gl=!1,(Hr!==null||Qr!==null)&&(Vp(),Up())}}function gs(e,t){var n=e.stateNode;if(n===null)return null;var r=za(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var vu=!1;if(rn)try{var Wo={};Object.defineProperty(Wo,"passive",{get:function(){vu=!0}}),window.addEventListener("test",Wo,Wo),window.removeEventListener("test",Wo,Wo)}catch{vu=!1}function Px(e,t,n,r,o,s,i,a,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var ss=!1,Zi=null,Ji=!1,yu=null,kx={onError:function(e){ss=!0,Zi=e}};function Tx(e,t,n,r,o,s,i,a,l){ss=!1,Zi=null,Px.apply(kx,arguments)}function Rx(e,t,n,r,o,s,i,a,l){if(Tx.apply(this,arguments),ss){if(ss){var c=Zi;ss=!1,Zi=null}else throw Error(R(198));Ji||(Ji=!0,yu=c)}}function br(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Qp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function af(e){if(br(e)!==e)throw Error(R(188))}function jx(e){var t=e.alternate;if(!t){if(t=br(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return af(o),e;if(s===r)return af(o),t;s=s.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i){for(a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function Yp(e){return e=jx(e),e!==null?Kp(e):null}function Kp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Kp(e);if(t!==null)return t;e=e.sibling}return null}var Gp=ut.unstable_scheduleCallback,lf=ut.unstable_cancelCallback,Ox=ut.unstable_shouldYield,Mx=ut.unstable_requestPaint,we=ut.unstable_now,_x=ut.unstable_getCurrentPriorityLevel,Lc=ut.unstable_ImmediatePriority,qp=ut.unstable_UserBlockingPriority,ea=ut.unstable_NormalPriority,Ax=ut.unstable_LowPriority,Xp=ut.unstable_IdlePriority,Da=null,Ht=null;function Dx(e){if(Ht&&typeof Ht.onCommitFiberRoot=="function")try{Ht.onCommitFiberRoot(Da,e,void 0,(e.current.flags&128)===128)}catch{}}var Rt=Math.clz32?Math.clz32:Fx,Ix=Math.log,Lx=Math.LN2;function Fx(e){return e>>>=0,e===0?32:31-(Ix(e)/Lx|0)|0}var ui=64,ci=4194304;function es(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ta(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~o;a!==0?r=es(a):(s&=i,s!==0&&(r=es(s)))}else i=n&~o,i!==0?r=es(i):s!==0&&(r=es(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Rt(t),o=1<<n,r|=e[n],t&=~o;return r}function zx(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function $x(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-Rt(s),a=1<<i,l=o[i];l===-1?(!(a&n)||a&r)&&(o[i]=zx(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function xu(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Zp(){var e=ui;return ui<<=1,!(ui&4194240)&&(ui=64),e}function vl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Hs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Rt(t),e[t]=n}function Wx(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Rt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function Fc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Rt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ne=0;function Jp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var em,zc,tm,nm,rm,wu=!1,di=[],On=null,Mn=null,_n=null,vs=new Map,ys=new Map,wn=[],Ux="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function uf(e,t){switch(e){case"focusin":case"focusout":On=null;break;case"dragenter":case"dragleave":Mn=null;break;case"mouseover":case"mouseout":_n=null;break;case"pointerover":case"pointerout":vs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ys.delete(t.pointerId)}}function Uo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=Ys(t),t!==null&&zc(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Bx(e,t,n,r,o){switch(t){case"focusin":return On=Uo(On,e,t,n,r,o),!0;case"dragenter":return Mn=Uo(Mn,e,t,n,r,o),!0;case"mouseover":return _n=Uo(_n,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return vs.set(s,Uo(vs.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,ys.set(s,Uo(ys.get(s)||null,e,t,n,r,o)),!0}return!1}function om(e){var t=Jn(e.target);if(t!==null){var n=br(t);if(n!==null){if(t=n.tag,t===13){if(t=Qp(n),t!==null){e.blockedOn=t,rm(e.priority,function(){tm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ai(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Su(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);mu=r,n.target.dispatchEvent(r),mu=null}else return t=Ys(n),t!==null&&zc(t),e.blockedOn=n,!1;t.shift()}return!0}function cf(e,t,n){Ai(e)&&n.delete(t)}function Vx(){wu=!1,On!==null&&Ai(On)&&(On=null),Mn!==null&&Ai(Mn)&&(Mn=null),_n!==null&&Ai(_n)&&(_n=null),vs.forEach(cf),ys.forEach(cf)}function Bo(e,t){e.blockedOn===t&&(e.blockedOn=null,wu||(wu=!0,ut.unstable_scheduleCallback(ut.unstable_NormalPriority,Vx)))}function xs(e){function t(o){return Bo(o,e)}if(0<di.length){Bo(di[0],e);for(var n=1;n<di.length;n++){var r=di[n];r.blockedOn===e&&(r.blockedOn=null)}}for(On!==null&&Bo(On,e),Mn!==null&&Bo(Mn,e),_n!==null&&Bo(_n,e),vs.forEach(t),ys.forEach(t),n=0;n<wn.length;n++)r=wn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<wn.length&&(n=wn[0],n.blockedOn===null);)om(n),n.blockedOn===null&&wn.shift()}var Yr=cn.ReactCurrentBatchConfig,na=!0;function Hx(e,t,n,r){var o=ne,s=Yr.transition;Yr.transition=null;try{ne=1,$c(e,t,n,r)}finally{ne=o,Yr.transition=s}}function Qx(e,t,n,r){var o=ne,s=Yr.transition;Yr.transition=null;try{ne=4,$c(e,t,n,r)}finally{ne=o,Yr.transition=s}}function $c(e,t,n,r){if(na){var o=Su(e,t,n,r);if(o===null)kl(e,t,r,ra,n),uf(e,r);else if(Bx(o,e,t,n,r))r.stopPropagation();else if(uf(e,r),t&4&&-1<Ux.indexOf(e)){for(;o!==null;){var s=Ys(o);if(s!==null&&em(s),s=Su(e,t,n,r),s===null&&kl(e,t,r,ra,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else kl(e,t,r,null,n)}}var ra=null;function Su(e,t,n,r){if(ra=null,e=Ic(r),e=Jn(e),e!==null)if(t=br(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Qp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ra=e,null}function sm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(_x()){case Lc:return 1;case qp:return 4;case ea:case Ax:return 16;case Xp:return 536870912;default:return 16}default:return 16}}var Rn=null,Wc=null,Di=null;function im(){if(Di)return Di;var e,t=Wc,n=t.length,r,o="value"in Rn?Rn.value:Rn.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return Di=o.slice(e,1<r?1-r:void 0)}function Ii(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function fi(){return!0}function df(){return!1}function ft(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?fi:df,this.isPropagationStopped=df,this}return ve(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=fi)},persist:function(){},isPersistent:fi}),t}var To={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Uc=ft(To),Qs=ve({},To,{view:0,detail:0}),Yx=ft(Qs),yl,xl,Vo,Ia=ve({},Qs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Bc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Vo&&(Vo&&e.type==="mousemove"?(yl=e.screenX-Vo.screenX,xl=e.screenY-Vo.screenY):xl=yl=0,Vo=e),yl)},movementY:function(e){return"movementY"in e?e.movementY:xl}}),ff=ft(Ia),Kx=ve({},Ia,{dataTransfer:0}),Gx=ft(Kx),qx=ve({},Qs,{relatedTarget:0}),wl=ft(qx),Xx=ve({},To,{animationName:0,elapsedTime:0,pseudoElement:0}),Zx=ft(Xx),Jx=ve({},To,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ew=ft(Jx),tw=ve({},To,{data:0}),hf=ft(tw),nw={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},rw={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ow={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function sw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ow[e])?!!t[e]:!1}function Bc(){return sw}var iw=ve({},Qs,{key:function(e){if(e.key){var t=nw[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ii(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?rw[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Bc,charCode:function(e){return e.type==="keypress"?Ii(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ii(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),aw=ft(iw),lw=ve({},Ia,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pf=ft(lw),uw=ve({},Qs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Bc}),cw=ft(uw),dw=ve({},To,{propertyName:0,elapsedTime:0,pseudoElement:0}),fw=ft(dw),hw=ve({},Ia,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),pw=ft(hw),mw=[9,13,27,32],Vc=rn&&"CompositionEvent"in window,is=null;rn&&"documentMode"in document&&(is=document.documentMode);var gw=rn&&"TextEvent"in window&&!is,am=rn&&(!Vc||is&&8<is&&11>=is),mf=" ",gf=!1;function lm(e,t){switch(e){case"keyup":return mw.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function um(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ar=!1;function vw(e,t){switch(e){case"compositionend":return um(t);case"keypress":return t.which!==32?null:(gf=!0,mf);case"textInput":return e=t.data,e===mf&&gf?null:e;default:return null}}function yw(e,t){if(Ar)return e==="compositionend"||!Vc&&lm(e,t)?(e=im(),Di=Wc=Rn=null,Ar=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return am&&t.locale!=="ko"?null:t.data;default:return null}}var xw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function vf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!xw[e.type]:t==="textarea"}function cm(e,t,n,r){Wp(r),t=oa(t,"onChange"),0<t.length&&(n=new Uc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var as=null,ws=null;function ww(e){Sm(e,0)}function La(e){var t=Lr(e);if(Ap(t))return e}function Sw(e,t){if(e==="change")return t}var dm=!1;if(rn){var Sl;if(rn){var Cl="oninput"in document;if(!Cl){var yf=document.createElement("div");yf.setAttribute("oninput","return;"),Cl=typeof yf.oninput=="function"}Sl=Cl}else Sl=!1;dm=Sl&&(!document.documentMode||9<document.documentMode)}function xf(){as&&(as.detachEvent("onpropertychange",fm),ws=as=null)}function fm(e){if(e.propertyName==="value"&&La(ws)){var t=[];cm(t,ws,e,Ic(e)),Hp(ww,t)}}function Cw(e,t,n){e==="focusin"?(xf(),as=t,ws=n,as.attachEvent("onpropertychange",fm)):e==="focusout"&&xf()}function Ew(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return La(ws)}function bw(e,t){if(e==="click")return La(t)}function Nw(e,t){if(e==="input"||e==="change")return La(t)}function Pw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ot=typeof Object.is=="function"?Object.is:Pw;function Ss(e,t){if(Ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ru.call(t,o)||!Ot(e[o],t[o]))return!1}return!0}function wf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sf(e,t){var n=wf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wf(n)}}function hm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?hm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function pm(){for(var e=window,t=Xi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Xi(e.document)}return t}function Hc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function kw(e){var t=pm(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&hm(n.ownerDocument.documentElement,n)){if(r!==null&&Hc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=Sf(n,s);var i=Sf(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Tw=rn&&"documentMode"in document&&11>=document.documentMode,Dr=null,Cu=null,ls=null,Eu=!1;function Cf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Eu||Dr==null||Dr!==Xi(r)||(r=Dr,"selectionStart"in r&&Hc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ls&&Ss(ls,r)||(ls=r,r=oa(Cu,"onSelect"),0<r.length&&(t=new Uc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Dr)))}function hi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ir={animationend:hi("Animation","AnimationEnd"),animationiteration:hi("Animation","AnimationIteration"),animationstart:hi("Animation","AnimationStart"),transitionend:hi("Transition","TransitionEnd")},El={},mm={};rn&&(mm=document.createElement("div").style,"AnimationEvent"in window||(delete Ir.animationend.animation,delete Ir.animationiteration.animation,delete Ir.animationstart.animation),"TransitionEvent"in window||delete Ir.transitionend.transition);function Fa(e){if(El[e])return El[e];if(!Ir[e])return e;var t=Ir[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in mm)return El[e]=t[n];return e}var gm=Fa("animationend"),vm=Fa("animationiteration"),ym=Fa("animationstart"),xm=Fa("transitionend"),wm=new Map,Ef="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Hn(e,t){wm.set(e,t),Er(t,[e])}for(var bl=0;bl<Ef.length;bl++){var Nl=Ef[bl],Rw=Nl.toLowerCase(),jw=Nl[0].toUpperCase()+Nl.slice(1);Hn(Rw,"on"+jw)}Hn(gm,"onAnimationEnd");Hn(vm,"onAnimationIteration");Hn(ym,"onAnimationStart");Hn("dblclick","onDoubleClick");Hn("focusin","onFocus");Hn("focusout","onBlur");Hn(xm,"onTransitionEnd");go("onMouseEnter",["mouseout","mouseover"]);go("onMouseLeave",["mouseout","mouseover"]);go("onPointerEnter",["pointerout","pointerover"]);go("onPointerLeave",["pointerout","pointerover"]);Er("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Er("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Er("onBeforeInput",["compositionend","keypress","textInput","paste"]);Er("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Er("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Er("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ts="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ow=new Set("cancel close invalid load scroll toggle".split(" ").concat(ts));function bf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Rx(r,t,void 0,e),e.currentTarget=null}function Sm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],l=a.instance,c=a.currentTarget;if(a=a.listener,l!==s&&o.isPropagationStopped())break e;bf(o,a,c),s=l}else for(i=0;i<r.length;i++){if(a=r[i],l=a.instance,c=a.currentTarget,a=a.listener,l!==s&&o.isPropagationStopped())break e;bf(o,a,c),s=l}}}if(Ji)throw e=yu,Ji=!1,yu=null,e}function de(e,t){var n=t[Tu];n===void 0&&(n=t[Tu]=new Set);var r=e+"__bubble";n.has(r)||(Cm(t,e,2,!1),n.add(r))}function Pl(e,t,n){var r=0;t&&(r|=4),Cm(n,e,r,t)}var pi="_reactListening"+Math.random().toString(36).slice(2);function Cs(e){if(!e[pi]){e[pi]=!0,Rp.forEach(function(n){n!=="selectionchange"&&(Ow.has(n)||Pl(n,!1,e),Pl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[pi]||(t[pi]=!0,Pl("selectionchange",!1,t))}}function Cm(e,t,n,r){switch(sm(t)){case 1:var o=Hx;break;case 4:o=Qx;break;default:o=$c}n=o.bind(null,t,n,e),o=void 0,!vu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function kl(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var l=i.tag;if((l===3||l===4)&&(l=i.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;i=i.return}for(;a!==null;){if(i=Jn(a),i===null)return;if(l=i.tag,l===5||l===6){r=s=i;continue e}a=a.parentNode}}r=r.return}Hp(function(){var c=s,d=Ic(n),f=[];e:{var h=wm.get(e);if(h!==void 0){var x=Uc,S=e;switch(e){case"keypress":if(Ii(n)===0)break e;case"keydown":case"keyup":x=aw;break;case"focusin":S="focus",x=wl;break;case"focusout":S="blur",x=wl;break;case"beforeblur":case"afterblur":x=wl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=ff;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=Gx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=cw;break;case gm:case vm:case ym:x=Zx;break;case xm:x=fw;break;case"scroll":x=Yx;break;case"wheel":x=pw;break;case"copy":case"cut":case"paste":x=ew;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=pf}var m=(t&4)!==0,w=!m&&e==="scroll",v=m?h!==null?h+"Capture":null:h;m=[];for(var g=c,y;g!==null;){y=g;var C=y.stateNode;if(y.tag===5&&C!==null&&(y=C,v!==null&&(C=gs(g,v),C!=null&&m.push(Es(g,C,y)))),w)break;g=g.return}0<m.length&&(h=new x(h,S,null,n,d),f.push({event:h,listeners:m}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",h&&n!==mu&&(S=n.relatedTarget||n.fromElement)&&(Jn(S)||S[on]))break e;if((x||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,x?(S=n.relatedTarget||n.toElement,x=c,S=S?Jn(S):null,S!==null&&(w=br(S),S!==w||S.tag!==5&&S.tag!==6)&&(S=null)):(x=null,S=c),x!==S)){if(m=ff,C="onMouseLeave",v="onMouseEnter",g="mouse",(e==="pointerout"||e==="pointerover")&&(m=pf,C="onPointerLeave",v="onPointerEnter",g="pointer"),w=x==null?h:Lr(x),y=S==null?h:Lr(S),h=new m(C,g+"leave",x,n,d),h.target=w,h.relatedTarget=y,C=null,Jn(d)===c&&(m=new m(v,g+"enter",S,n,d),m.target=y,m.relatedTarget=w,C=m),w=C,x&&S)t:{for(m=x,v=S,g=0,y=m;y;y=Pr(y))g++;for(y=0,C=v;C;C=Pr(C))y++;for(;0<g-y;)m=Pr(m),g--;for(;0<y-g;)v=Pr(v),y--;for(;g--;){if(m===v||v!==null&&m===v.alternate)break t;m=Pr(m),v=Pr(v)}m=null}else m=null;x!==null&&Nf(f,h,x,m,!1),S!==null&&w!==null&&Nf(f,w,S,m,!0)}}e:{if(h=c?Lr(c):window,x=h.nodeName&&h.nodeName.toLowerCase(),x==="select"||x==="input"&&h.type==="file")var b=Sw;else if(vf(h))if(dm)b=Nw;else{b=Ew;var P=Cw}else(x=h.nodeName)&&x.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(b=bw);if(b&&(b=b(e,c))){cm(f,b,n,d);break e}P&&P(e,h,c),e==="focusout"&&(P=h._wrapperState)&&P.controlled&&h.type==="number"&&cu(h,"number",h.value)}switch(P=c?Lr(c):window,e){case"focusin":(vf(P)||P.contentEditable==="true")&&(Dr=P,Cu=c,ls=null);break;case"focusout":ls=Cu=Dr=null;break;case"mousedown":Eu=!0;break;case"contextmenu":case"mouseup":case"dragend":Eu=!1,Cf(f,n,d);break;case"selectionchange":if(Tw)break;case"keydown":case"keyup":Cf(f,n,d)}var N;if(Vc)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Ar?lm(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(am&&n.locale!=="ko"&&(Ar||k!=="onCompositionStart"?k==="onCompositionEnd"&&Ar&&(N=im()):(Rn=d,Wc="value"in Rn?Rn.value:Rn.textContent,Ar=!0)),P=oa(c,k),0<P.length&&(k=new hf(k,e,null,n,d),f.push({event:k,listeners:P}),N?k.data=N:(N=um(n),N!==null&&(k.data=N)))),(N=gw?vw(e,n):yw(e,n))&&(c=oa(c,"onBeforeInput"),0<c.length&&(d=new hf("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:c}),d.data=N))}Sm(f,t)})}function Es(e,t,n){return{instance:e,listener:t,currentTarget:n}}function oa(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=gs(e,n),s!=null&&r.unshift(Es(e,s,o)),s=gs(e,t),s!=null&&r.push(Es(e,s,o))),e=e.return}return r}function Pr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Nf(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var a=n,l=a.alternate,c=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&c!==null&&(a=c,o?(l=gs(n,s),l!=null&&i.unshift(Es(n,l,a))):o||(l=gs(n,s),l!=null&&i.push(Es(n,l,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Mw=/\r\n?/g,_w=/\u0000|\uFFFD/g;function Pf(e){return(typeof e=="string"?e:""+e).replace(Mw,`
`).replace(_w,"")}function mi(e,t,n){if(t=Pf(t),Pf(e)!==t&&n)throw Error(R(425))}function sa(){}var bu=null,Nu=null;function Pu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ku=typeof setTimeout=="function"?setTimeout:void 0,Aw=typeof clearTimeout=="function"?clearTimeout:void 0,kf=typeof Promise=="function"?Promise:void 0,Dw=typeof queueMicrotask=="function"?queueMicrotask:typeof kf<"u"?function(e){return kf.resolve(null).then(e).catch(Iw)}:ku;function Iw(e){setTimeout(function(){throw e})}function Tl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),xs(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);xs(t)}function An(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Tf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Ro=Math.random().toString(36).slice(2),Bt="__reactFiber$"+Ro,bs="__reactProps$"+Ro,on="__reactContainer$"+Ro,Tu="__reactEvents$"+Ro,Lw="__reactListeners$"+Ro,Fw="__reactHandles$"+Ro;function Jn(e){var t=e[Bt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[on]||n[Bt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Tf(e);e!==null;){if(n=e[Bt])return n;e=Tf(e)}return t}e=n,n=e.parentNode}return null}function Ys(e){return e=e[Bt]||e[on],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Lr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function za(e){return e[bs]||null}var Ru=[],Fr=-1;function Qn(e){return{current:e}}function fe(e){0>Fr||(e.current=Ru[Fr],Ru[Fr]=null,Fr--)}function le(e,t){Fr++,Ru[Fr]=e.current,e.current=t}var $n={},$e=Qn($n),Je=Qn(!1),pr=$n;function vo(e,t){var n=e.type.contextTypes;if(!n)return $n;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function et(e){return e=e.childContextTypes,e!=null}function ia(){fe(Je),fe($e)}function Rf(e,t,n){if($e.current!==$n)throw Error(R(168));le($e,t),le(Je,n)}function Em(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,Cx(e)||"Unknown",o));return ve({},n,r)}function aa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||$n,pr=$e.current,le($e,e),le(Je,Je.current),!0}function jf(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=Em(e,t,pr),r.__reactInternalMemoizedMergedChildContext=e,fe(Je),fe($e),le($e,e)):fe(Je),le(Je,n)}var Jt=null,$a=!1,Rl=!1;function bm(e){Jt===null?Jt=[e]:Jt.push(e)}function zw(e){$a=!0,bm(e)}function Yn(){if(!Rl&&Jt!==null){Rl=!0;var e=0,t=ne;try{var n=Jt;for(ne=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Jt=null,$a=!1}catch(o){throw Jt!==null&&(Jt=Jt.slice(e+1)),Gp(Lc,Yn),o}finally{ne=t,Rl=!1}}return null}var zr=[],$r=0,la=null,ua=0,mt=[],gt=0,mr=null,en=1,tn="";function qn(e,t){zr[$r++]=ua,zr[$r++]=la,la=e,ua=t}function Nm(e,t,n){mt[gt++]=en,mt[gt++]=tn,mt[gt++]=mr,mr=e;var r=en;e=tn;var o=32-Rt(r)-1;r&=~(1<<o),n+=1;var s=32-Rt(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,en=1<<32-Rt(t)+o|n<<o|r,tn=s+e}else en=1<<s|n<<o|r,tn=e}function Qc(e){e.return!==null&&(qn(e,1),Nm(e,1,0))}function Yc(e){for(;e===la;)la=zr[--$r],zr[$r]=null,ua=zr[--$r],zr[$r]=null;for(;e===mr;)mr=mt[--gt],mt[gt]=null,tn=mt[--gt],mt[gt]=null,en=mt[--gt],mt[gt]=null}var at=null,it=null,pe=!1,kt=null;function Pm(e,t){var n=vt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Of(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,at=e,it=An(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,at=e,it=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=mr!==null?{id:en,overflow:tn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=vt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,at=e,it=null,!0):!1;default:return!1}}function ju(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ou(e){if(pe){var t=it;if(t){var n=t;if(!Of(e,t)){if(ju(e))throw Error(R(418));t=An(n.nextSibling);var r=at;t&&Of(e,t)?Pm(r,n):(e.flags=e.flags&-4097|2,pe=!1,at=e)}}else{if(ju(e))throw Error(R(418));e.flags=e.flags&-4097|2,pe=!1,at=e}}}function Mf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;at=e}function gi(e){if(e!==at)return!1;if(!pe)return Mf(e),pe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Pu(e.type,e.memoizedProps)),t&&(t=it)){if(ju(e))throw km(),Error(R(418));for(;t;)Pm(e,t),t=An(t.nextSibling)}if(Mf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){it=An(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}it=null}}else it=at?An(e.stateNode.nextSibling):null;return!0}function km(){for(var e=it;e;)e=An(e.nextSibling)}function yo(){it=at=null,pe=!1}function Kc(e){kt===null?kt=[e]:kt.push(e)}var $w=cn.ReactCurrentBatchConfig;function Ho(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var a=o.refs;i===null?delete a[s]:a[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function vi(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function _f(e){var t=e._init;return t(e._payload)}function Tm(e){function t(v,g){if(e){var y=v.deletions;y===null?(v.deletions=[g],v.flags|=16):y.push(g)}}function n(v,g){if(!e)return null;for(;g!==null;)t(v,g),g=g.sibling;return null}function r(v,g){for(v=new Map;g!==null;)g.key!==null?v.set(g.key,g):v.set(g.index,g),g=g.sibling;return v}function o(v,g){return v=Fn(v,g),v.index=0,v.sibling=null,v}function s(v,g,y){return v.index=y,e?(y=v.alternate,y!==null?(y=y.index,y<g?(v.flags|=2,g):y):(v.flags|=2,g)):(v.flags|=1048576,g)}function i(v){return e&&v.alternate===null&&(v.flags|=2),v}function a(v,g,y,C){return g===null||g.tag!==6?(g=Il(y,v.mode,C),g.return=v,g):(g=o(g,y),g.return=v,g)}function l(v,g,y,C){var b=y.type;return b===_r?d(v,g,y.props.children,C,y.key):g!==null&&(g.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===yn&&_f(b)===g.type)?(C=o(g,y.props),C.ref=Ho(v,g,y),C.return=v,C):(C=Bi(y.type,y.key,y.props,null,v.mode,C),C.ref=Ho(v,g,y),C.return=v,C)}function c(v,g,y,C){return g===null||g.tag!==4||g.stateNode.containerInfo!==y.containerInfo||g.stateNode.implementation!==y.implementation?(g=Ll(y,v.mode,C),g.return=v,g):(g=o(g,y.children||[]),g.return=v,g)}function d(v,g,y,C,b){return g===null||g.tag!==7?(g=cr(y,v.mode,C,b),g.return=v,g):(g=o(g,y),g.return=v,g)}function f(v,g,y){if(typeof g=="string"&&g!==""||typeof g=="number")return g=Il(""+g,v.mode,y),g.return=v,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case ii:return y=Bi(g.type,g.key,g.props,null,v.mode,y),y.ref=Ho(v,null,g),y.return=v,y;case Mr:return g=Ll(g,v.mode,y),g.return=v,g;case yn:var C=g._init;return f(v,C(g._payload),y)}if(Jo(g)||$o(g))return g=cr(g,v.mode,y,null),g.return=v,g;vi(v,g)}return null}function h(v,g,y,C){var b=g!==null?g.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return b!==null?null:a(v,g,""+y,C);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ii:return y.key===b?l(v,g,y,C):null;case Mr:return y.key===b?c(v,g,y,C):null;case yn:return b=y._init,h(v,g,b(y._payload),C)}if(Jo(y)||$o(y))return b!==null?null:d(v,g,y,C,null);vi(v,y)}return null}function x(v,g,y,C,b){if(typeof C=="string"&&C!==""||typeof C=="number")return v=v.get(y)||null,a(g,v,""+C,b);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case ii:return v=v.get(C.key===null?y:C.key)||null,l(g,v,C,b);case Mr:return v=v.get(C.key===null?y:C.key)||null,c(g,v,C,b);case yn:var P=C._init;return x(v,g,y,P(C._payload),b)}if(Jo(C)||$o(C))return v=v.get(y)||null,d(g,v,C,b,null);vi(g,C)}return null}function S(v,g,y,C){for(var b=null,P=null,N=g,k=g=0,j=null;N!==null&&k<y.length;k++){N.index>k?(j=N,N=null):j=N.sibling;var O=h(v,N,y[k],C);if(O===null){N===null&&(N=j);break}e&&N&&O.alternate===null&&t(v,N),g=s(O,g,k),P===null?b=O:P.sibling=O,P=O,N=j}if(k===y.length)return n(v,N),pe&&qn(v,k),b;if(N===null){for(;k<y.length;k++)N=f(v,y[k],C),N!==null&&(g=s(N,g,k),P===null?b=N:P.sibling=N,P=N);return pe&&qn(v,k),b}for(N=r(v,N);k<y.length;k++)j=x(N,v,k,y[k],C),j!==null&&(e&&j.alternate!==null&&N.delete(j.key===null?k:j.key),g=s(j,g,k),P===null?b=j:P.sibling=j,P=j);return e&&N.forEach(function(F){return t(v,F)}),pe&&qn(v,k),b}function m(v,g,y,C){var b=$o(y);if(typeof b!="function")throw Error(R(150));if(y=b.call(y),y==null)throw Error(R(151));for(var P=b=null,N=g,k=g=0,j=null,O=y.next();N!==null&&!O.done;k++,O=y.next()){N.index>k?(j=N,N=null):j=N.sibling;var F=h(v,N,O.value,C);if(F===null){N===null&&(N=j);break}e&&N&&F.alternate===null&&t(v,N),g=s(F,g,k),P===null?b=F:P.sibling=F,P=F,N=j}if(O.done)return n(v,N),pe&&qn(v,k),b;if(N===null){for(;!O.done;k++,O=y.next())O=f(v,O.value,C),O!==null&&(g=s(O,g,k),P===null?b=O:P.sibling=O,P=O);return pe&&qn(v,k),b}for(N=r(v,N);!O.done;k++,O=y.next())O=x(N,v,k,O.value,C),O!==null&&(e&&O.alternate!==null&&N.delete(O.key===null?k:O.key),g=s(O,g,k),P===null?b=O:P.sibling=O,P=O);return e&&N.forEach(function(A){return t(v,A)}),pe&&qn(v,k),b}function w(v,g,y,C){if(typeof y=="object"&&y!==null&&y.type===_r&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ii:e:{for(var b=y.key,P=g;P!==null;){if(P.key===b){if(b=y.type,b===_r){if(P.tag===7){n(v,P.sibling),g=o(P,y.props.children),g.return=v,v=g;break e}}else if(P.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===yn&&_f(b)===P.type){n(v,P.sibling),g=o(P,y.props),g.ref=Ho(v,P,y),g.return=v,v=g;break e}n(v,P);break}else t(v,P);P=P.sibling}y.type===_r?(g=cr(y.props.children,v.mode,C,y.key),g.return=v,v=g):(C=Bi(y.type,y.key,y.props,null,v.mode,C),C.ref=Ho(v,g,y),C.return=v,v=C)}return i(v);case Mr:e:{for(P=y.key;g!==null;){if(g.key===P)if(g.tag===4&&g.stateNode.containerInfo===y.containerInfo&&g.stateNode.implementation===y.implementation){n(v,g.sibling),g=o(g,y.children||[]),g.return=v,v=g;break e}else{n(v,g);break}else t(v,g);g=g.sibling}g=Ll(y,v.mode,C),g.return=v,v=g}return i(v);case yn:return P=y._init,w(v,g,P(y._payload),C)}if(Jo(y))return S(v,g,y,C);if($o(y))return m(v,g,y,C);vi(v,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,g!==null&&g.tag===6?(n(v,g.sibling),g=o(g,y),g.return=v,v=g):(n(v,g),g=Il(y,v.mode,C),g.return=v,v=g),i(v)):n(v,g)}return w}var xo=Tm(!0),Rm=Tm(!1),ca=Qn(null),da=null,Wr=null,Gc=null;function qc(){Gc=Wr=da=null}function Xc(e){var t=ca.current;fe(ca),e._currentValue=t}function Mu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Kr(e,t){da=e,Gc=Wr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ze=!0),e.firstContext=null)}function xt(e){var t=e._currentValue;if(Gc!==e)if(e={context:e,memoizedValue:t,next:null},Wr===null){if(da===null)throw Error(R(308));Wr=e,da.dependencies={lanes:0,firstContext:e}}else Wr=Wr.next=e;return t}var er=null;function Zc(e){er===null?er=[e]:er.push(e)}function jm(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Zc(t)):(n.next=o.next,o.next=n),t.interleaved=n,sn(e,r)}function sn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xn=!1;function Jc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Om(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function nn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Dn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Z&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,sn(e,n)}return o=r.interleaved,o===null?(t.next=t,Zc(r)):(t.next=o.next,o.next=t),r.interleaved=t,sn(e,n)}function Li(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fc(e,n)}}function Af(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function fa(e,t,n,r){var o=e.updateQueue;xn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,c=l.next;l.next=null,i===null?s=c:i.next=c,i=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==i&&(a===null?d.firstBaseUpdate=c:a.next=c,d.lastBaseUpdate=l))}if(s!==null){var f=o.baseState;i=0,d=c=l=null,a=s;do{var h=a.lane,x=a.eventTime;if((r&h)===h){d!==null&&(d=d.next={eventTime:x,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var S=e,m=a;switch(h=t,x=n,m.tag){case 1:if(S=m.payload,typeof S=="function"){f=S.call(x,f,h);break e}f=S;break e;case 3:S.flags=S.flags&-65537|128;case 0:if(S=m.payload,h=typeof S=="function"?S.call(x,f,h):S,h==null)break e;f=ve({},f,h);break e;case 2:xn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,h=o.effects,h===null?o.effects=[a]:h.push(a))}else x={eventTime:x,lane:h,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(c=d=x,l=f):d=d.next=x,i|=h;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;h=a,a=h.next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}while(!0);if(d===null&&(l=f),o.baseState=l,o.firstBaseUpdate=c,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);vr|=i,e.lanes=i,e.memoizedState=f}}function Df(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var Ks={},Qt=Qn(Ks),Ns=Qn(Ks),Ps=Qn(Ks);function tr(e){if(e===Ks)throw Error(R(174));return e}function ed(e,t){switch(le(Ps,t),le(Ns,e),le(Qt,Ks),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:fu(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=fu(t,e)}fe(Qt),le(Qt,t)}function wo(){fe(Qt),fe(Ns),fe(Ps)}function Mm(e){tr(Ps.current);var t=tr(Qt.current),n=fu(t,e.type);t!==n&&(le(Ns,e),le(Qt,n))}function td(e){Ns.current===e&&(fe(Qt),fe(Ns))}var me=Qn(0);function ha(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var jl=[];function nd(){for(var e=0;e<jl.length;e++)jl[e]._workInProgressVersionPrimary=null;jl.length=0}var Fi=cn.ReactCurrentDispatcher,Ol=cn.ReactCurrentBatchConfig,gr=0,ge=null,be=null,Te=null,pa=!1,us=!1,ks=0,Ww=0;function De(){throw Error(R(321))}function rd(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ot(e[n],t[n]))return!1;return!0}function od(e,t,n,r,o,s){if(gr=s,ge=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Fi.current=e===null||e.memoizedState===null?Hw:Qw,e=n(r,o),us){s=0;do{if(us=!1,ks=0,25<=s)throw Error(R(301));s+=1,Te=be=null,t.updateQueue=null,Fi.current=Yw,e=n(r,o)}while(us)}if(Fi.current=ma,t=be!==null&&be.next!==null,gr=0,Te=be=ge=null,pa=!1,t)throw Error(R(300));return e}function sd(){var e=ks!==0;return ks=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Te===null?ge.memoizedState=Te=e:Te=Te.next=e,Te}function wt(){if(be===null){var e=ge.alternate;e=e!==null?e.memoizedState:null}else e=be.next;var t=Te===null?ge.memoizedState:Te.next;if(t!==null)Te=t,be=e;else{if(e===null)throw Error(R(310));be=e,e={memoizedState:be.memoizedState,baseState:be.baseState,baseQueue:be.baseQueue,queue:be.queue,next:null},Te===null?ge.memoizedState=Te=e:Te=Te.next=e}return Te}function Ts(e,t){return typeof t=="function"?t(e):t}function Ml(e){var t=wt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=be,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var a=i=null,l=null,c=s;do{var d=c.lane;if((gr&d)===d)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(a=l=f,i=r):l=l.next=f,ge.lanes|=d,vr|=d}c=c.next}while(c!==null&&c!==s);l===null?i=r:l.next=a,Ot(r,t.memoizedState)||(Ze=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,ge.lanes|=s,vr|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function _l(e){var t=wt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);Ot(s,t.memoizedState)||(Ze=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function _m(){}function Am(e,t){var n=ge,r=wt(),o=t(),s=!Ot(r.memoizedState,o);if(s&&(r.memoizedState=o,Ze=!0),r=r.queue,id(Lm.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||Te!==null&&Te.memoizedState.tag&1){if(n.flags|=2048,Rs(9,Im.bind(null,n,r,o,t),void 0,null),Re===null)throw Error(R(349));gr&30||Dm(n,t,o)}return o}function Dm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ge.updateQueue,t===null?(t={lastEffect:null,stores:null},ge.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Im(e,t,n,r){t.value=n,t.getSnapshot=r,Fm(t)&&zm(e)}function Lm(e,t,n){return n(function(){Fm(t)&&zm(e)})}function Fm(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ot(e,n)}catch{return!0}}function zm(e){var t=sn(e,1);t!==null&&jt(t,e,1,-1)}function If(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ts,lastRenderedState:e},t.queue=e,e=e.dispatch=Vw.bind(null,ge,e),[t.memoizedState,e]}function Rs(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ge.updateQueue,t===null?(t={lastEffect:null,stores:null},ge.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function $m(){return wt().memoizedState}function zi(e,t,n,r){var o=Ft();ge.flags|=e,o.memoizedState=Rs(1|t,n,void 0,r===void 0?null:r)}function Wa(e,t,n,r){var o=wt();r=r===void 0?null:r;var s=void 0;if(be!==null){var i=be.memoizedState;if(s=i.destroy,r!==null&&rd(r,i.deps)){o.memoizedState=Rs(t,n,s,r);return}}ge.flags|=e,o.memoizedState=Rs(1|t,n,s,r)}function Lf(e,t){return zi(8390656,8,e,t)}function id(e,t){return Wa(2048,8,e,t)}function Wm(e,t){return Wa(4,2,e,t)}function Um(e,t){return Wa(4,4,e,t)}function Bm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vm(e,t,n){return n=n!=null?n.concat([e]):null,Wa(4,4,Bm.bind(null,t,e),n)}function ad(){}function Hm(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&rd(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Qm(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&rd(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ym(e,t,n){return gr&21?(Ot(n,t)||(n=Zp(),ge.lanes|=n,vr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ze=!0),e.memoizedState=n)}function Uw(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var r=Ol.transition;Ol.transition={};try{e(!1),t()}finally{ne=n,Ol.transition=r}}function Km(){return wt().memoizedState}function Bw(e,t,n){var r=Ln(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gm(e))qm(t,n);else if(n=jm(e,t,n,r),n!==null){var o=Ye();jt(n,e,r,o),Xm(n,t,r)}}function Vw(e,t,n){var r=Ln(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gm(e))qm(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,a=s(i,n);if(o.hasEagerState=!0,o.eagerState=a,Ot(a,i)){var l=t.interleaved;l===null?(o.next=o,Zc(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=jm(e,t,o,r),n!==null&&(o=Ye(),jt(n,e,r,o),Xm(n,t,r))}}function Gm(e){var t=e.alternate;return e===ge||t!==null&&t===ge}function qm(e,t){us=pa=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xm(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fc(e,n)}}var ma={readContext:xt,useCallback:De,useContext:De,useEffect:De,useImperativeHandle:De,useInsertionEffect:De,useLayoutEffect:De,useMemo:De,useReducer:De,useRef:De,useState:De,useDebugValue:De,useDeferredValue:De,useTransition:De,useMutableSource:De,useSyncExternalStore:De,useId:De,unstable_isNewReconciler:!1},Hw={readContext:xt,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:xt,useEffect:Lf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,zi(4194308,4,Bm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return zi(4194308,4,e,t)},useInsertionEffect:function(e,t){return zi(4,2,e,t)},useMemo:function(e,t){var n=Ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ft();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Bw.bind(null,ge,e),[r.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:If,useDebugValue:ad,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=If(!1),t=e[0];return e=Uw.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ge,o=Ft();if(pe){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),Re===null)throw Error(R(349));gr&30||Dm(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,Lf(Lm.bind(null,r,s,e),[e]),r.flags|=2048,Rs(9,Im.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Ft(),t=Re.identifierPrefix;if(pe){var n=tn,r=en;n=(r&~(1<<32-Rt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ks++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ww++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Qw={readContext:xt,useCallback:Hm,useContext:xt,useEffect:id,useImperativeHandle:Vm,useInsertionEffect:Wm,useLayoutEffect:Um,useMemo:Qm,useReducer:Ml,useRef:$m,useState:function(){return Ml(Ts)},useDebugValue:ad,useDeferredValue:function(e){var t=wt();return Ym(t,be.memoizedState,e)},useTransition:function(){var e=Ml(Ts)[0],t=wt().memoizedState;return[e,t]},useMutableSource:_m,useSyncExternalStore:Am,useId:Km,unstable_isNewReconciler:!1},Yw={readContext:xt,useCallback:Hm,useContext:xt,useEffect:id,useImperativeHandle:Vm,useInsertionEffect:Wm,useLayoutEffect:Um,useMemo:Qm,useReducer:_l,useRef:$m,useState:function(){return _l(Ts)},useDebugValue:ad,useDeferredValue:function(e){var t=wt();return be===null?t.memoizedState=e:Ym(t,be.memoizedState,e)},useTransition:function(){var e=_l(Ts)[0],t=wt().memoizedState;return[e,t]},useMutableSource:_m,useSyncExternalStore:Am,useId:Km,unstable_isNewReconciler:!1};function bt(e,t){if(e&&e.defaultProps){t=ve({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function _u(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ve({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ua={isMounted:function(e){return(e=e._reactInternals)?br(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ye(),o=Ln(e),s=nn(r,o);s.payload=t,n!=null&&(s.callback=n),t=Dn(e,s,o),t!==null&&(jt(t,e,o,r),Li(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ye(),o=Ln(e),s=nn(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Dn(e,s,o),t!==null&&(jt(t,e,o,r),Li(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ye(),r=Ln(e),o=nn(n,r);o.tag=2,t!=null&&(o.callback=t),t=Dn(e,o,r),t!==null&&(jt(t,e,r,n),Li(t,e,r))}};function Ff(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!Ss(n,r)||!Ss(o,s):!0}function Zm(e,t,n){var r=!1,o=$n,s=t.contextType;return typeof s=="object"&&s!==null?s=xt(s):(o=et(t)?pr:$e.current,r=t.contextTypes,s=(r=r!=null)?vo(e,o):$n),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ua,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function zf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ua.enqueueReplaceState(t,t.state,null)}function Au(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Jc(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=xt(s):(s=et(t)?pr:$e.current,o.context=vo(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(_u(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ua.enqueueReplaceState(o,o.state,null),fa(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function So(e,t){try{var n="",r=t;do n+=Sx(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function Al(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Du(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Kw=typeof WeakMap=="function"?WeakMap:Map;function Jm(e,t,n){n=nn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){va||(va=!0,Hu=r),Du(e,t)},n}function eg(e,t,n){n=nn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Du(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Du(e,t),typeof r!="function"&&(In===null?In=new Set([this]):In.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function $f(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Kw;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=l1.bind(null,e,t,n),t.then(e,e))}function Wf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Uf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=nn(-1,1),t.tag=2,Dn(n,t,1))),n.lanes|=1),e)}var Gw=cn.ReactCurrentOwner,Ze=!1;function Qe(e,t,n,r){t.child=e===null?Rm(t,null,n,r):xo(t,e.child,n,r)}function Bf(e,t,n,r,o){n=n.render;var s=t.ref;return Kr(t,o),r=od(e,t,n,r,s,o),n=sd(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,an(e,t,o)):(pe&&n&&Qc(t),t.flags|=1,Qe(e,t,r,o),t.child)}function Vf(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!md(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,tg(e,t,s,r,o)):(e=Bi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:Ss,n(i,r)&&e.ref===t.ref)return an(e,t,o)}return t.flags|=1,e=Fn(s,r),e.ref=t.ref,e.return=t,t.child=e}function tg(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(Ss(s,r)&&e.ref===t.ref)if(Ze=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Ze=!0);else return t.lanes=e.lanes,an(e,t,o)}return Iu(e,t,n,r,o)}function ng(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},le(Br,ot),ot|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,le(Br,ot),ot|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,le(Br,ot),ot|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,le(Br,ot),ot|=r;return Qe(e,t,o,n),t.child}function rg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Iu(e,t,n,r,o){var s=et(n)?pr:$e.current;return s=vo(t,s),Kr(t,o),n=od(e,t,n,r,s,o),r=sd(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,an(e,t,o)):(pe&&r&&Qc(t),t.flags|=1,Qe(e,t,n,o),t.child)}function Hf(e,t,n,r,o){if(et(n)){var s=!0;aa(t)}else s=!1;if(Kr(t,o),t.stateNode===null)$i(e,t),Zm(t,n,r),Au(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var l=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=xt(c):(c=et(n)?pr:$e.current,c=vo(t,c));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||l!==c)&&zf(t,i,r,c),xn=!1;var h=t.memoizedState;i.state=h,fa(t,r,i,o),l=t.memoizedState,a!==r||h!==l||Je.current||xn?(typeof d=="function"&&(_u(t,n,d,r),l=t.memoizedState),(a=xn||Ff(t,n,a,r,h,l,c))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=c,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Om(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:bt(t.type,a),i.props=c,f=t.pendingProps,h=i.context,l=n.contextType,typeof l=="object"&&l!==null?l=xt(l):(l=et(n)?pr:$e.current,l=vo(t,l));var x=n.getDerivedStateFromProps;(d=typeof x=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==f||h!==l)&&zf(t,i,r,l),xn=!1,h=t.memoizedState,i.state=h,fa(t,r,i,o);var S=t.memoizedState;a!==f||h!==S||Je.current||xn?(typeof x=="function"&&(_u(t,n,x,r),S=t.memoizedState),(c=xn||Ff(t,n,c,r,h,S,l)||!1)?(d||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,S,l),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,S,l)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=S),i.props=r,i.state=S,i.context=l,r=c):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Lu(e,t,n,r,s,o)}function Lu(e,t,n,r,o,s){rg(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&jf(t,n,!1),an(e,t,s);r=t.stateNode,Gw.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=xo(t,e.child,null,s),t.child=xo(t,null,a,s)):Qe(e,t,a,s),t.memoizedState=r.state,o&&jf(t,n,!0),t.child}function og(e){var t=e.stateNode;t.pendingContext?Rf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Rf(e,t.context,!1),ed(e,t.containerInfo)}function Qf(e,t,n,r,o){return yo(),Kc(o),t.flags|=256,Qe(e,t,n,r),t.child}var Fu={dehydrated:null,treeContext:null,retryLane:0};function zu(e){return{baseLanes:e,cachePool:null,transitions:null}}function sg(e,t,n){var r=t.pendingProps,o=me.current,s=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),le(me,o&1),e===null)return Ou(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=Ha(i,r,0,null),e=cr(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=zu(n),t.memoizedState=Fu,e):ld(t,i));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return qw(e,t,i,r,a,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Fn(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?s=Fn(a,s):(s=cr(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?zu(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=Fu,r}return s=e.child,e=s.sibling,r=Fn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ld(e,t){return t=Ha({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function yi(e,t,n,r){return r!==null&&Kc(r),xo(t,e.child,null,n),e=ld(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function qw(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=Al(Error(R(422))),yi(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=Ha({mode:"visible",children:r.children},o,0,null),s=cr(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&xo(t,e.child,null,i),t.child.memoizedState=zu(i),t.memoizedState=Fu,s);if(!(t.mode&1))return yi(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(R(419)),r=Al(s,r,void 0),yi(e,t,i,r)}if(a=(i&e.childLanes)!==0,Ze||a){if(r=Re,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,sn(e,o),jt(r,e,o,-1))}return pd(),r=Al(Error(R(421))),yi(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=u1.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,it=An(o.nextSibling),at=t,pe=!0,kt=null,e!==null&&(mt[gt++]=en,mt[gt++]=tn,mt[gt++]=mr,en=e.id,tn=e.overflow,mr=t),t=ld(t,r.children),t.flags|=4096,t)}function Yf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Mu(e.return,t,n)}function Dl(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function ig(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(Qe(e,t,r.children,n),r=me.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Yf(e,n,t);else if(e.tag===19)Yf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(le(me,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&ha(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Dl(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&ha(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Dl(t,!0,n,null,s);break;case"together":Dl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $i(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function an(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),vr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Fn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Fn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Xw(e,t,n){switch(t.tag){case 3:og(t),yo();break;case 5:Mm(t);break;case 1:et(t.type)&&aa(t);break;case 4:ed(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;le(ca,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(le(me,me.current&1),t.flags|=128,null):n&t.child.childLanes?sg(e,t,n):(le(me,me.current&1),e=an(e,t,n),e!==null?e.sibling:null);le(me,me.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ig(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),le(me,me.current),r)break;return null;case 22:case 23:return t.lanes=0,ng(e,t,n)}return an(e,t,n)}var ag,$u,lg,ug;ag=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};$u=function(){};lg=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,tr(Qt.current);var s=null;switch(n){case"input":o=lu(e,o),r=lu(e,r),s=[];break;case"select":o=ve({},o,{value:void 0}),r=ve({},r,{value:void 0}),s=[];break;case"textarea":o=du(e,o),r=du(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=sa)}hu(n,r);var i;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var a=o[c];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(ps.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var l=r[c];if(a=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&l!==a&&(l!=null||a!=null))if(c==="style")if(a){for(i in a)!a.hasOwnProperty(i)||l&&l.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in l)l.hasOwnProperty(i)&&a[i]!==l[i]&&(n||(n={}),n[i]=l[i])}else n||(s||(s=[]),s.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(ps.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&de("scroll",e),s||a===l||(s=[])):(s=s||[]).push(c,l))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}};ug=function(e,t,n,r){n!==r&&(t.flags|=4)};function Qo(e,t){if(!pe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ie(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Zw(e,t,n){var r=t.pendingProps;switch(Yc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ie(t),null;case 1:return et(t.type)&&ia(),Ie(t),null;case 3:return r=t.stateNode,wo(),fe(Je),fe($e),nd(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(gi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,kt!==null&&(Ku(kt),kt=null))),$u(e,t),Ie(t),null;case 5:td(t);var o=tr(Ps.current);if(n=t.type,e!==null&&t.stateNode!=null)lg(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Ie(t),null}if(e=tr(Qt.current),gi(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Bt]=t,r[bs]=s,e=(t.mode&1)!==0,n){case"dialog":de("cancel",r),de("close",r);break;case"iframe":case"object":case"embed":de("load",r);break;case"video":case"audio":for(o=0;o<ts.length;o++)de(ts[o],r);break;case"source":de("error",r);break;case"img":case"image":case"link":de("error",r),de("load",r);break;case"details":de("toggle",r);break;case"input":tf(r,s),de("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},de("invalid",r);break;case"textarea":rf(r,s),de("invalid",r)}hu(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&mi(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&mi(r.textContent,a,e),o=["children",""+a]):ps.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&de("scroll",r)}switch(n){case"input":ai(r),nf(r,s,!0);break;case"textarea":ai(r),of(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=sa)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Lp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Bt]=t,e[bs]=r,ag(e,t,!1,!1),t.stateNode=e;e:{switch(i=pu(n,r),n){case"dialog":de("cancel",e),de("close",e),o=r;break;case"iframe":case"object":case"embed":de("load",e),o=r;break;case"video":case"audio":for(o=0;o<ts.length;o++)de(ts[o],e);o=r;break;case"source":de("error",e),o=r;break;case"img":case"image":case"link":de("error",e),de("load",e),o=r;break;case"details":de("toggle",e),o=r;break;case"input":tf(e,r),o=lu(e,r),de("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ve({},r,{value:void 0}),de("invalid",e);break;case"textarea":rf(e,r),o=du(e,r),de("invalid",e);break;default:o=r}hu(n,o),a=o;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?$p(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Fp(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ms(e,l):typeof l=="number"&&ms(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(ps.hasOwnProperty(s)?l!=null&&s==="onScroll"&&de("scroll",e):l!=null&&Mc(e,s,l,i))}switch(n){case"input":ai(e),nf(e,r,!1);break;case"textarea":ai(e),of(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Vr(e,!!r.multiple,s,!1):r.defaultValue!=null&&Vr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=sa)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ie(t),null;case 6:if(e&&t.stateNode!=null)ug(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=tr(Ps.current),tr(Qt.current),gi(t)){if(r=t.stateNode,n=t.memoizedProps,r[Bt]=t,(s=r.nodeValue!==n)&&(e=at,e!==null))switch(e.tag){case 3:mi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&mi(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Bt]=t,t.stateNode=r}return Ie(t),null;case 13:if(fe(me),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(pe&&it!==null&&t.mode&1&&!(t.flags&128))km(),yo(),t.flags|=98560,s=!1;else if(s=gi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(R(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(R(317));s[Bt]=t}else yo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ie(t),s=!1}else kt!==null&&(Ku(kt),kt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||me.current&1?ke===0&&(ke=3):pd())),t.updateQueue!==null&&(t.flags|=4),Ie(t),null);case 4:return wo(),$u(e,t),e===null&&Cs(t.stateNode.containerInfo),Ie(t),null;case 10:return Xc(t.type._context),Ie(t),null;case 17:return et(t.type)&&ia(),Ie(t),null;case 19:if(fe(me),s=t.memoizedState,s===null)return Ie(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)Qo(s,!1);else{if(ke!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=ha(e),i!==null){for(t.flags|=128,Qo(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return le(me,me.current&1|2),t.child}e=e.sibling}s.tail!==null&&we()>Co&&(t.flags|=128,r=!0,Qo(s,!1),t.lanes=4194304)}else{if(!r)if(e=ha(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Qo(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!pe)return Ie(t),null}else 2*we()-s.renderingStartTime>Co&&n!==1073741824&&(t.flags|=128,r=!0,Qo(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=we(),t.sibling=null,n=me.current,le(me,r?n&1|2:n&1),t):(Ie(t),null);case 22:case 23:return hd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ot&1073741824&&(Ie(t),t.subtreeFlags&6&&(t.flags|=8192)):Ie(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function Jw(e,t){switch(Yc(t),t.tag){case 1:return et(t.type)&&ia(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return wo(),fe(Je),fe($e),nd(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return td(t),null;case 13:if(fe(me),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));yo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return fe(me),null;case 4:return wo(),null;case 10:return Xc(t.type._context),null;case 22:case 23:return hd(),null;case 24:return null;default:return null}}var xi=!1,Fe=!1,e1=typeof WeakSet=="function"?WeakSet:Set,D=null;function Ur(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){xe(e,t,r)}else n.current=null}function Wu(e,t,n){try{n()}catch(r){xe(e,t,r)}}var Kf=!1;function t1(e,t){if(bu=na,e=pm(),Hc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,a=-1,l=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var x;f!==n||o!==0&&f.nodeType!==3||(a=i+o),f!==s||r!==0&&f.nodeType!==3||(l=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(x=f.firstChild)!==null;)h=f,f=x;for(;;){if(f===e)break t;if(h===n&&++c===o&&(a=i),h===s&&++d===r&&(l=i),(x=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=x}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Nu={focusedElem:e,selectionRange:n},na=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var S=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(S!==null){var m=S.memoizedProps,w=S.memoizedState,v=t.stateNode,g=v.getSnapshotBeforeUpdate(t.elementType===t.type?m:bt(t.type,m),w);v.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(C){xe(t,t.return,C)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return S=Kf,Kf=!1,S}function cs(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&Wu(t,n,s)}o=o.next}while(o!==r)}}function Ba(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Uu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function cg(e){var t=e.alternate;t!==null&&(e.alternate=null,cg(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Bt],delete t[bs],delete t[Tu],delete t[Lw],delete t[Fw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function dg(e){return e.tag===5||e.tag===3||e.tag===4}function Gf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||dg(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Bu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=sa));else if(r!==4&&(e=e.child,e!==null))for(Bu(e,t,n),e=e.sibling;e!==null;)Bu(e,t,n),e=e.sibling}function Vu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Vu(e,t,n),e=e.sibling;e!==null;)Vu(e,t,n),e=e.sibling}var je=null,Pt=!1;function dn(e,t,n){for(n=n.child;n!==null;)fg(e,t,n),n=n.sibling}function fg(e,t,n){if(Ht&&typeof Ht.onCommitFiberUnmount=="function")try{Ht.onCommitFiberUnmount(Da,n)}catch{}switch(n.tag){case 5:Fe||Ur(n,t);case 6:var r=je,o=Pt;je=null,dn(e,t,n),je=r,Pt=o,je!==null&&(Pt?(e=je,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):je.removeChild(n.stateNode));break;case 18:je!==null&&(Pt?(e=je,n=n.stateNode,e.nodeType===8?Tl(e.parentNode,n):e.nodeType===1&&Tl(e,n),xs(e)):Tl(je,n.stateNode));break;case 4:r=je,o=Pt,je=n.stateNode.containerInfo,Pt=!0,dn(e,t,n),je=r,Pt=o;break;case 0:case 11:case 14:case 15:if(!Fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&Wu(n,t,i),o=o.next}while(o!==r)}dn(e,t,n);break;case 1:if(!Fe&&(Ur(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){xe(n,t,a)}dn(e,t,n);break;case 21:dn(e,t,n);break;case 22:n.mode&1?(Fe=(r=Fe)||n.memoizedState!==null,dn(e,t,n),Fe=r):dn(e,t,n);break;default:dn(e,t,n)}}function qf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new e1),t.forEach(function(r){var o=c1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function St(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:je=a.stateNode,Pt=!1;break e;case 3:je=a.stateNode.containerInfo,Pt=!0;break e;case 4:je=a.stateNode.containerInfo,Pt=!0;break e}a=a.return}if(je===null)throw Error(R(160));fg(s,i,o),je=null,Pt=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(c){xe(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)hg(t,e),t=t.sibling}function hg(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(St(t,e),Lt(e),r&4){try{cs(3,e,e.return),Ba(3,e)}catch(m){xe(e,e.return,m)}try{cs(5,e,e.return)}catch(m){xe(e,e.return,m)}}break;case 1:St(t,e),Lt(e),r&512&&n!==null&&Ur(n,n.return);break;case 5:if(St(t,e),Lt(e),r&512&&n!==null&&Ur(n,n.return),e.flags&32){var o=e.stateNode;try{ms(o,"")}catch(m){xe(e,e.return,m)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&Dp(o,s),pu(a,i);var c=pu(a,s);for(i=0;i<l.length;i+=2){var d=l[i],f=l[i+1];d==="style"?$p(o,f):d==="dangerouslySetInnerHTML"?Fp(o,f):d==="children"?ms(o,f):Mc(o,d,f,c)}switch(a){case"input":uu(o,s);break;case"textarea":Ip(o,s);break;case"select":var h=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var x=s.value;x!=null?Vr(o,!!s.multiple,x,!1):h!==!!s.multiple&&(s.defaultValue!=null?Vr(o,!!s.multiple,s.defaultValue,!0):Vr(o,!!s.multiple,s.multiple?[]:"",!1))}o[bs]=s}catch(m){xe(e,e.return,m)}}break;case 6:if(St(t,e),Lt(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(m){xe(e,e.return,m)}}break;case 3:if(St(t,e),Lt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{xs(t.containerInfo)}catch(m){xe(e,e.return,m)}break;case 4:St(t,e),Lt(e);break;case 13:St(t,e),Lt(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(dd=we())),r&4&&qf(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Fe=(c=Fe)||d,St(t,e),Fe=c):St(t,e),Lt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(D=e,d=e.child;d!==null;){for(f=D=d;D!==null;){switch(h=D,x=h.child,h.tag){case 0:case 11:case 14:case 15:cs(4,h,h.return);break;case 1:Ur(h,h.return);var S=h.stateNode;if(typeof S.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,S.props=t.memoizedProps,S.state=t.memoizedState,S.componentWillUnmount()}catch(m){xe(r,n,m)}}break;case 5:Ur(h,h.return);break;case 22:if(h.memoizedState!==null){Zf(f);continue}}x!==null?(x.return=h,D=x):Zf(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{o=f.stateNode,c?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=f.stateNode,l=f.memoizedProps.style,i=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=zp("display",i))}catch(m){xe(e,e.return,m)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(m){xe(e,e.return,m)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:St(t,e),Lt(e),r&4&&qf(e);break;case 21:break;default:St(t,e),Lt(e)}}function Lt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(dg(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(ms(o,""),r.flags&=-33);var s=Gf(e);Vu(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,a=Gf(e);Bu(e,a,i);break;default:throw Error(R(161))}}catch(l){xe(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function n1(e,t,n){D=e,pg(e)}function pg(e,t,n){for(var r=(e.mode&1)!==0;D!==null;){var o=D,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||xi;if(!i){var a=o.alternate,l=a!==null&&a.memoizedState!==null||Fe;a=xi;var c=Fe;if(xi=i,(Fe=l)&&!c)for(D=o;D!==null;)i=D,l=i.child,i.tag===22&&i.memoizedState!==null?Jf(o):l!==null?(l.return=i,D=l):Jf(o);for(;s!==null;)D=s,pg(s),s=s.sibling;D=o,xi=a,Fe=c}Xf(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,D=s):Xf(e)}}function Xf(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Fe||Ba(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Fe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:bt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Df(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Df(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&xs(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Fe||t.flags&512&&Uu(t)}catch(h){xe(t,t.return,h)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function Zf(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function Jf(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ba(4,t)}catch(l){xe(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){xe(t,o,l)}}var s=t.return;try{Uu(t)}catch(l){xe(t,s,l)}break;case 5:var i=t.return;try{Uu(t)}catch(l){xe(t,i,l)}}}catch(l){xe(t,t.return,l)}if(t===e){D=null;break}var a=t.sibling;if(a!==null){a.return=t.return,D=a;break}D=t.return}}var r1=Math.ceil,ga=cn.ReactCurrentDispatcher,ud=cn.ReactCurrentOwner,yt=cn.ReactCurrentBatchConfig,Z=0,Re=null,Ce=null,Me=0,ot=0,Br=Qn(0),ke=0,js=null,vr=0,Va=0,cd=0,ds=null,Xe=null,dd=0,Co=1/0,Zt=null,va=!1,Hu=null,In=null,wi=!1,jn=null,ya=0,fs=0,Qu=null,Wi=-1,Ui=0;function Ye(){return Z&6?we():Wi!==-1?Wi:Wi=we()}function Ln(e){return e.mode&1?Z&2&&Me!==0?Me&-Me:$w.transition!==null?(Ui===0&&(Ui=Zp()),Ui):(e=ne,e!==0||(e=window.event,e=e===void 0?16:sm(e.type)),e):1}function jt(e,t,n,r){if(50<fs)throw fs=0,Qu=null,Error(R(185));Hs(e,n,r),(!(Z&2)||e!==Re)&&(e===Re&&(!(Z&2)&&(Va|=n),ke===4&&Sn(e,Me)),tt(e,r),n===1&&Z===0&&!(t.mode&1)&&(Co=we()+500,$a&&Yn()))}function tt(e,t){var n=e.callbackNode;$x(e,t);var r=ta(e,e===Re?Me:0);if(r===0)n!==null&&lf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lf(n),t===1)e.tag===0?zw(eh.bind(null,e)):bm(eh.bind(null,e)),Dw(function(){!(Z&6)&&Yn()}),n=null;else{switch(Jp(r)){case 1:n=Lc;break;case 4:n=qp;break;case 16:n=ea;break;case 536870912:n=Xp;break;default:n=ea}n=Cg(n,mg.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function mg(e,t){if(Wi=-1,Ui=0,Z&6)throw Error(R(327));var n=e.callbackNode;if(Gr()&&e.callbackNode!==n)return null;var r=ta(e,e===Re?Me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=xa(e,r);else{t=r;var o=Z;Z|=2;var s=vg();(Re!==e||Me!==t)&&(Zt=null,Co=we()+500,ur(e,t));do try{i1();break}catch(a){gg(e,a)}while(!0);qc(),ga.current=s,Z=o,Ce!==null?t=0:(Re=null,Me=0,t=ke)}if(t!==0){if(t===2&&(o=xu(e),o!==0&&(r=o,t=Yu(e,o))),t===1)throw n=js,ur(e,0),Sn(e,r),tt(e,we()),n;if(t===6)Sn(e,r);else{if(o=e.current.alternate,!(r&30)&&!o1(o)&&(t=xa(e,r),t===2&&(s=xu(e),s!==0&&(r=s,t=Yu(e,s))),t===1))throw n=js,ur(e,0),Sn(e,r),tt(e,we()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Xn(e,Xe,Zt);break;case 3:if(Sn(e,r),(r&130023424)===r&&(t=dd+500-we(),10<t)){if(ta(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ye(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ku(Xn.bind(null,e,Xe,Zt),t);break}Xn(e,Xe,Zt);break;case 4:if(Sn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-Rt(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=we()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*r1(r/1960))-r,10<r){e.timeoutHandle=ku(Xn.bind(null,e,Xe,Zt),r);break}Xn(e,Xe,Zt);break;case 5:Xn(e,Xe,Zt);break;default:throw Error(R(329))}}}return tt(e,we()),e.callbackNode===n?mg.bind(null,e):null}function Yu(e,t){var n=ds;return e.current.memoizedState.isDehydrated&&(ur(e,t).flags|=256),e=xa(e,t),e!==2&&(t=Xe,Xe=n,t!==null&&Ku(t)),e}function Ku(e){Xe===null?Xe=e:Xe.push.apply(Xe,e)}function o1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!Ot(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Sn(e,t){for(t&=~cd,t&=~Va,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Rt(t),r=1<<n;e[n]=-1,t&=~r}}function eh(e){if(Z&6)throw Error(R(327));Gr();var t=ta(e,0);if(!(t&1))return tt(e,we()),null;var n=xa(e,t);if(e.tag!==0&&n===2){var r=xu(e);r!==0&&(t=r,n=Yu(e,r))}if(n===1)throw n=js,ur(e,0),Sn(e,t),tt(e,we()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Xn(e,Xe,Zt),tt(e,we()),null}function fd(e,t){var n=Z;Z|=1;try{return e(t)}finally{Z=n,Z===0&&(Co=we()+500,$a&&Yn())}}function yr(e){jn!==null&&jn.tag===0&&!(Z&6)&&Gr();var t=Z;Z|=1;var n=yt.transition,r=ne;try{if(yt.transition=null,ne=1,e)return e()}finally{ne=r,yt.transition=n,Z=t,!(Z&6)&&Yn()}}function hd(){ot=Br.current,fe(Br)}function ur(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Aw(n)),Ce!==null)for(n=Ce.return;n!==null;){var r=n;switch(Yc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ia();break;case 3:wo(),fe(Je),fe($e),nd();break;case 5:td(r);break;case 4:wo();break;case 13:fe(me);break;case 19:fe(me);break;case 10:Xc(r.type._context);break;case 22:case 23:hd()}n=n.return}if(Re=e,Ce=e=Fn(e.current,null),Me=ot=t,ke=0,js=null,cd=Va=vr=0,Xe=ds=null,er!==null){for(t=0;t<er.length;t++)if(n=er[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}er=null}return e}function gg(e,t){do{var n=Ce;try{if(qc(),Fi.current=ma,pa){for(var r=ge.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}pa=!1}if(gr=0,Te=be=ge=null,us=!1,ks=0,ud.current=null,n===null||n.return===null){ke=1,js=t,Ce=null;break}e:{var s=e,i=n.return,a=n,l=t;if(t=Me,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,d=a,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var x=Wf(i);if(x!==null){x.flags&=-257,Uf(x,i,a,s,t),x.mode&1&&$f(s,c,t),t=x,l=c;var S=t.updateQueue;if(S===null){var m=new Set;m.add(l),t.updateQueue=m}else S.add(l);break e}else{if(!(t&1)){$f(s,c,t),pd();break e}l=Error(R(426))}}else if(pe&&a.mode&1){var w=Wf(i);if(w!==null){!(w.flags&65536)&&(w.flags|=256),Uf(w,i,a,s,t),Kc(So(l,a));break e}}s=l=So(l,a),ke!==4&&(ke=2),ds===null?ds=[s]:ds.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var v=Jm(s,l,t);Af(s,v);break e;case 1:a=l;var g=s.type,y=s.stateNode;if(!(s.flags&128)&&(typeof g.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(In===null||!In.has(y)))){s.flags|=65536,t&=-t,s.lanes|=t;var C=eg(s,a,t);Af(s,C);break e}}s=s.return}while(s!==null)}xg(n)}catch(b){t=b,Ce===n&&n!==null&&(Ce=n=n.return);continue}break}while(!0)}function vg(){var e=ga.current;return ga.current=ma,e===null?ma:e}function pd(){(ke===0||ke===3||ke===2)&&(ke=4),Re===null||!(vr&268435455)&&!(Va&268435455)||Sn(Re,Me)}function xa(e,t){var n=Z;Z|=2;var r=vg();(Re!==e||Me!==t)&&(Zt=null,ur(e,t));do try{s1();break}catch(o){gg(e,o)}while(!0);if(qc(),Z=n,ga.current=r,Ce!==null)throw Error(R(261));return Re=null,Me=0,ke}function s1(){for(;Ce!==null;)yg(Ce)}function i1(){for(;Ce!==null&&!Ox();)yg(Ce)}function yg(e){var t=Sg(e.alternate,e,ot);e.memoizedProps=e.pendingProps,t===null?xg(e):Ce=t,ud.current=null}function xg(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Jw(n,t),n!==null){n.flags&=32767,Ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ke=6,Ce=null;return}}else if(n=Zw(n,t,ot),n!==null){Ce=n;return}if(t=t.sibling,t!==null){Ce=t;return}Ce=t=e}while(t!==null);ke===0&&(ke=5)}function Xn(e,t,n){var r=ne,o=yt.transition;try{yt.transition=null,ne=1,a1(e,t,n,r)}finally{yt.transition=o,ne=r}return null}function a1(e,t,n,r){do Gr();while(jn!==null);if(Z&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Wx(e,s),e===Re&&(Ce=Re=null,Me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||wi||(wi=!0,Cg(ea,function(){return Gr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=yt.transition,yt.transition=null;var i=ne;ne=1;var a=Z;Z|=4,ud.current=null,t1(e,n),hg(n,e),kw(Nu),na=!!bu,Nu=bu=null,e.current=n,n1(n),Mx(),Z=a,ne=i,yt.transition=s}else e.current=n;if(wi&&(wi=!1,jn=e,ya=o),s=e.pendingLanes,s===0&&(In=null),Dx(n.stateNode),tt(e,we()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(va)throw va=!1,e=Hu,Hu=null,e;return ya&1&&e.tag!==0&&Gr(),s=e.pendingLanes,s&1?e===Qu?fs++:(fs=0,Qu=e):fs=0,Yn(),null}function Gr(){if(jn!==null){var e=Jp(ya),t=yt.transition,n=ne;try{if(yt.transition=null,ne=16>e?16:e,jn===null)var r=!1;else{if(e=jn,jn=null,ya=0,Z&6)throw Error(R(331));var o=Z;for(Z|=4,D=e.current;D!==null;){var s=D,i=s.child;if(D.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var c=a[l];for(D=c;D!==null;){var d=D;switch(d.tag){case 0:case 11:case 15:cs(8,d,s)}var f=d.child;if(f!==null)f.return=d,D=f;else for(;D!==null;){d=D;var h=d.sibling,x=d.return;if(cg(d),d===c){D=null;break}if(h!==null){h.return=x,D=h;break}D=x}}}var S=s.alternate;if(S!==null){var m=S.child;if(m!==null){S.child=null;do{var w=m.sibling;m.sibling=null,m=w}while(m!==null)}}D=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,D=i;else e:for(;D!==null;){if(s=D,s.flags&2048)switch(s.tag){case 0:case 11:case 15:cs(9,s,s.return)}var v=s.sibling;if(v!==null){v.return=s.return,D=v;break e}D=s.return}}var g=e.current;for(D=g;D!==null;){i=D;var y=i.child;if(i.subtreeFlags&2064&&y!==null)y.return=i,D=y;else e:for(i=g;D!==null;){if(a=D,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ba(9,a)}}catch(b){xe(a,a.return,b)}if(a===i){D=null;break e}var C=a.sibling;if(C!==null){C.return=a.return,D=C;break e}D=a.return}}if(Z=o,Yn(),Ht&&typeof Ht.onPostCommitFiberRoot=="function")try{Ht.onPostCommitFiberRoot(Da,e)}catch{}r=!0}return r}finally{ne=n,yt.transition=t}}return!1}function th(e,t,n){t=So(n,t),t=Jm(e,t,1),e=Dn(e,t,1),t=Ye(),e!==null&&(Hs(e,1,t),tt(e,t))}function xe(e,t,n){if(e.tag===3)th(e,e,n);else for(;t!==null;){if(t.tag===3){th(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(In===null||!In.has(r))){e=So(n,e),e=eg(t,e,1),t=Dn(t,e,1),e=Ye(),t!==null&&(Hs(t,1,e),tt(t,e));break}}t=t.return}}function l1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ye(),e.pingedLanes|=e.suspendedLanes&n,Re===e&&(Me&n)===n&&(ke===4||ke===3&&(Me&130023424)===Me&&500>we()-dd?ur(e,0):cd|=n),tt(e,t)}function wg(e,t){t===0&&(e.mode&1?(t=ci,ci<<=1,!(ci&130023424)&&(ci=4194304)):t=1);var n=Ye();e=sn(e,t),e!==null&&(Hs(e,t,n),tt(e,n))}function u1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),wg(e,n)}function c1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),wg(e,n)}var Sg;Sg=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Je.current)Ze=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ze=!1,Xw(e,t,n);Ze=!!(e.flags&131072)}else Ze=!1,pe&&t.flags&1048576&&Nm(t,ua,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$i(e,t),e=t.pendingProps;var o=vo(t,$e.current);Kr(t,n),o=od(null,t,r,e,o,n);var s=sd();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,et(r)?(s=!0,aa(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Jc(t),o.updater=Ua,t.stateNode=o,o._reactInternals=t,Au(t,r,e,n),t=Lu(null,t,r,!0,s,n)):(t.tag=0,pe&&s&&Qc(t),Qe(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch($i(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=f1(r),e=bt(r,e),o){case 0:t=Iu(null,t,r,e,n);break e;case 1:t=Hf(null,t,r,e,n);break e;case 11:t=Bf(null,t,r,e,n);break e;case 14:t=Vf(null,t,r,bt(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:bt(r,o),Iu(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:bt(r,o),Hf(e,t,r,o,n);case 3:e:{if(og(t),e===null)throw Error(R(387));r=t.pendingProps,s=t.memoizedState,o=s.element,Om(e,t),fa(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=So(Error(R(423)),t),t=Qf(e,t,r,n,o);break e}else if(r!==o){o=So(Error(R(424)),t),t=Qf(e,t,r,n,o);break e}else for(it=An(t.stateNode.containerInfo.firstChild),at=t,pe=!0,kt=null,n=Rm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(yo(),r===o){t=an(e,t,n);break e}Qe(e,t,r,n)}t=t.child}return t;case 5:return Mm(t),e===null&&Ou(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,Pu(r,o)?i=null:s!==null&&Pu(r,s)&&(t.flags|=32),rg(e,t),Qe(e,t,i,n),t.child;case 6:return e===null&&Ou(t),null;case 13:return sg(e,t,n);case 4:return ed(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=xo(t,null,r,n):Qe(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:bt(r,o),Bf(e,t,r,o,n);case 7:return Qe(e,t,t.pendingProps,n),t.child;case 8:return Qe(e,t,t.pendingProps.children,n),t.child;case 12:return Qe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,le(ca,r._currentValue),r._currentValue=i,s!==null)if(Ot(s.value,i)){if(s.children===o.children&&!Je.current){t=an(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){i=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=nn(-1,n&-n),l.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?l.next=l:(l.next=d.next,d.next=l),c.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Mu(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(R(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Mu(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}Qe(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Kr(t,n),o=xt(o),r=r(o),t.flags|=1,Qe(e,t,r,n),t.child;case 14:return r=t.type,o=bt(r,t.pendingProps),o=bt(r.type,o),Vf(e,t,r,o,n);case 15:return tg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:bt(r,o),$i(e,t),t.tag=1,et(r)?(e=!0,aa(t)):e=!1,Kr(t,n),Zm(t,r,o),Au(t,r,o,n),Lu(null,t,r,!0,e,n);case 19:return ig(e,t,n);case 22:return ng(e,t,n)}throw Error(R(156,t.tag))};function Cg(e,t){return Gp(e,t)}function d1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function vt(e,t,n,r){return new d1(e,t,n,r)}function md(e){return e=e.prototype,!(!e||!e.isReactComponent)}function f1(e){if(typeof e=="function")return md(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ac)return 11;if(e===Dc)return 14}return 2}function Fn(e,t){var n=e.alternate;return n===null?(n=vt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Bi(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")md(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case _r:return cr(n.children,o,s,t);case _c:i=8,o|=8;break;case ou:return e=vt(12,n,t,o|2),e.elementType=ou,e.lanes=s,e;case su:return e=vt(13,n,t,o),e.elementType=su,e.lanes=s,e;case iu:return e=vt(19,n,t,o),e.elementType=iu,e.lanes=s,e;case Mp:return Ha(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case jp:i=10;break e;case Op:i=9;break e;case Ac:i=11;break e;case Dc:i=14;break e;case yn:i=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=vt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function cr(e,t,n,r){return e=vt(7,e,r,t),e.lanes=n,e}function Ha(e,t,n,r){return e=vt(22,e,r,t),e.elementType=Mp,e.lanes=n,e.stateNode={isHidden:!1},e}function Il(e,t,n){return e=vt(6,e,null,t),e.lanes=n,e}function Ll(e,t,n){return t=vt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function h1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vl(0),this.expirationTimes=vl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function gd(e,t,n,r,o,s,i,a,l){return e=new h1(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=vt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Jc(s),e}function p1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Mr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Eg(e){if(!e)return $n;e=e._reactInternals;e:{if(br(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(et(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(et(n))return Em(e,n,t)}return t}function bg(e,t,n,r,o,s,i,a,l){return e=gd(n,r,!0,e,o,s,i,a,l),e.context=Eg(null),n=e.current,r=Ye(),o=Ln(n),s=nn(r,o),s.callback=t??null,Dn(n,s,o),e.current.lanes=o,Hs(e,o,r),tt(e,r),e}function Qa(e,t,n,r){var o=t.current,s=Ye(),i=Ln(o);return n=Eg(n),t.context===null?t.context=n:t.pendingContext=n,t=nn(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Dn(o,t,i),e!==null&&(jt(e,o,i,s),Li(e,o,i)),i}function wa(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function vd(e,t){nh(e,t),(e=e.alternate)&&nh(e,t)}function m1(){return null}var Ng=typeof reportError=="function"?reportError:function(e){console.error(e)};function yd(e){this._internalRoot=e}Ya.prototype.render=yd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));Qa(e,t,null,null)};Ya.prototype.unmount=yd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;yr(function(){Qa(null,e,null,null)}),t[on]=null}};function Ya(e){this._internalRoot=e}Ya.prototype.unstable_scheduleHydration=function(e){if(e){var t=nm();e={blockedOn:null,target:e,priority:t};for(var n=0;n<wn.length&&t!==0&&t<wn[n].priority;n++);wn.splice(n,0,e),n===0&&om(e)}};function xd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ka(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rh(){}function g1(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var c=wa(i);s.call(c)}}var i=bg(t,r,e,0,null,!1,!1,"",rh);return e._reactRootContainer=i,e[on]=i.current,Cs(e.nodeType===8?e.parentNode:e),yr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var c=wa(l);a.call(c)}}var l=gd(e,0,!1,null,null,!1,!1,"",rh);return e._reactRootContainer=l,e[on]=l.current,Cs(e.nodeType===8?e.parentNode:e),yr(function(){Qa(t,l,n,r)}),l}function Ga(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var a=o;o=function(){var l=wa(i);a.call(l)}}Qa(t,i,e,o)}else i=g1(n,t,e,o,r);return wa(i)}em=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=es(t.pendingLanes);n!==0&&(Fc(t,n|1),tt(t,we()),!(Z&6)&&(Co=we()+500,Yn()))}break;case 13:yr(function(){var r=sn(e,1);if(r!==null){var o=Ye();jt(r,e,1,o)}}),vd(e,1)}};zc=function(e){if(e.tag===13){var t=sn(e,134217728);if(t!==null){var n=Ye();jt(t,e,134217728,n)}vd(e,134217728)}};tm=function(e){if(e.tag===13){var t=Ln(e),n=sn(e,t);if(n!==null){var r=Ye();jt(n,e,t,r)}vd(e,t)}};nm=function(){return ne};rm=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};gu=function(e,t,n){switch(t){case"input":if(uu(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=za(r);if(!o)throw Error(R(90));Ap(r),uu(r,o)}}}break;case"textarea":Ip(e,n);break;case"select":t=n.value,t!=null&&Vr(e,!!n.multiple,t,!1)}};Bp=fd;Vp=yr;var v1={usingClientEntryPoint:!1,Events:[Ys,Lr,za,Wp,Up,fd]},Yo={findFiberByHostInstance:Jn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},y1={bundleType:Yo.bundleType,version:Yo.version,rendererPackageName:Yo.rendererPackageName,rendererConfig:Yo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:cn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Yp(e),e===null?null:e.stateNode},findFiberByHostInstance:Yo.findFiberByHostInstance||m1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Si=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Si.isDisabled&&Si.supportsFiber)try{Da=Si.inject(y1),Ht=Si}catch{}}dt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=v1;dt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!xd(t))throw Error(R(200));return p1(e,t,null,n)};dt.createRoot=function(e,t){if(!xd(e))throw Error(R(299));var n=!1,r="",o=Ng;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=gd(e,1,!1,null,null,n,!1,r,o),e[on]=t.current,Cs(e.nodeType===8?e.parentNode:e),new yd(t)};dt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Yp(t),e=e===null?null:e.stateNode,e};dt.flushSync=function(e){return yr(e)};dt.hydrate=function(e,t,n){if(!Ka(t))throw Error(R(200));return Ga(null,e,t,!0,n)};dt.hydrateRoot=function(e,t,n){if(!xd(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=Ng;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=bg(t,null,e,1,n??null,o,!1,s,i),e[on]=t.current,Cs(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ya(t)};dt.render=function(e,t,n){if(!Ka(t))throw Error(R(200));return Ga(null,e,t,!1,n)};dt.unmountComponentAtNode=function(e){if(!Ka(e))throw Error(R(40));return e._reactRootContainer?(yr(function(){Ga(null,null,e,!1,function(){e._reactRootContainer=null,e[on]=null})}),!0):!1};dt.unstable_batchedUpdates=fd;dt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ka(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return Ga(e,t,n,!1,r)};dt.version="18.3.1-next-f1338f8080-20240426";function Pg(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Pg)}catch(e){console.error(e)}}Pg(),Pp.exports=dt;var Nr=Pp.exports;const x1=hp(Nr);var kg,oh=Nr;kg=oh.createRoot,oh.hydrateRoot;function w1(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,r,o,s,i=[],a="",l=e.split("/");for(l[0]||l.shift();o=l.shift();)n=o[0],n==="*"?(i.push(n),a+=o[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(r=o.indexOf("?",1),s=o.indexOf(".",1),i.push(o.substring(1,~r?r:~s?s:o.length)),a+=~r&&!~s?"(?:/([^/]+?))?":"/([^/]+?)",~s&&(a+=(~r?"?":"")+"\\"+o.substring(s))):a+="/"+o;return{keys:i,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var Tg={exports:{}},Rg={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Eo=p;function S1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var C1=typeof Object.is=="function"?Object.is:S1,E1=Eo.useState,b1=Eo.useEffect,N1=Eo.useLayoutEffect,P1=Eo.useDebugValue;function k1(e,t){var n=t(),r=E1({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return N1(function(){o.value=n,o.getSnapshot=t,Fl(o)&&s({inst:o})},[e,n,t]),b1(function(){return Fl(o)&&s({inst:o}),e(function(){Fl(o)&&s({inst:o})})},[e]),P1(n),n}function Fl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!C1(e,n)}catch{return!0}}function T1(e,t){return t()}var R1=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?T1:k1;Rg.useSyncExternalStore=Eo.useSyncExternalStore!==void 0?Eo.useSyncExternalStore:R1;Tg.exports=Rg;var j1=Tg.exports;const O1=bp.useInsertionEffect,M1=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",_1=M1?p.useLayoutEffect:p.useEffect,A1=O1||_1,jg=e=>{const t=p.useRef([e,(...n)=>t[0](...n)]).current;return A1(()=>{t[0]=e}),t[1]},D1="popstate",wd="pushState",Sd="replaceState",I1="hashchange",sh=[D1,wd,Sd,I1],L1=e=>{for(const t of sh)addEventListener(t,e);return()=>{for(const t of sh)removeEventListener(t,e)}},Og=(e,t)=>j1.useSyncExternalStore(L1,e,t),F1=()=>location.search,z1=({ssrSearch:e=""}={})=>Og(F1,()=>e),ih=()=>location.pathname,$1=({ssrPath:e}={})=>Og(ih,e?()=>e:ih),W1=(e,{replace:t=!1,state:n=null}={})=>history[t?Sd:wd](n,"",e),U1=(e={})=>[$1(e),W1],ah=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[ah]>"u"){for(const e of[wd,Sd]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),r=new Event(e);return r.arguments=arguments,dispatchEvent(r),n}}Object.defineProperty(window,ah,{value:!0})}const B1=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",Mg=(e="")=>e==="/"?"":e,V1=(e,t)=>e[0]==="~"?e.slice(1):Mg(t)+e,H1=(e="",t)=>B1(lh(Mg(e)),lh(t)),lh=e=>{try{return decodeURI(e)}catch{return e}},_g={hook:U1,searchHook:z1,parser:w1,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},Ag=p.createContext(_g),Gs=()=>p.useContext(Ag),Dg={},Ig=p.createContext(Dg),Q1=()=>p.useContext(Ig),qa=e=>{const[t,n]=e.hook(e);return[H1(e.base,t),jg((r,o)=>n(V1(r,e.base),o))]},Cd=()=>qa(Gs()),Lg=(e,t,n,r)=>{const{pattern:o,keys:s}=t instanceof RegExp?{keys:!1,pattern:t}:e(t||"*",r),i=o.exec(n)||[],[a,...l]=i;return a!==void 0?[!0,(()=>{const c=s!==!1?Object.fromEntries(s.map((f,h)=>[f,l[h]])):i.groups;let d={...l};return c&&Object.assign(d,c),d})(),...r?[a]:[]]:[!1,null]},Y1=({children:e,...t})=>{var d,f;const n=Gs(),r=t.hook?_g:n;let o=r;const[s,i]=((d=t.ssrPath)==null?void 0:d.split("?"))??[];i&&(t.ssrSearch=i,t.ssrPath=s),t.hrefs=t.hrefs??((f=t.hook)==null?void 0:f.hrefs);let a=p.useRef({}),l=a.current,c=l;for(let h in r){const x=h==="base"?r[h]+(t[h]||""):t[h]||r[h];l===c&&x!==c[h]&&(a.current=c={...c}),c[h]=x,x!==r[h]&&(o=c)}return p.createElement(Ag.Provider,{value:o,children:e})},uh=({children:e,component:t},n)=>t?p.createElement(t,{params:n}):typeof e=="function"?e(n):e,K1=e=>{let t=p.useRef(Dg),n=t.current;for(const r in e)e[r]!==n[r]&&(n=e);return Object.keys(e).length===0&&(n=e),t.current=n},Ci=({path:e,nest:t,match:n,...r})=>{const o=Gs(),[s]=qa(o),[i,a,l]=n??Lg(o.parser,e,s,t),c=K1({...Q1(),...a});if(!i)return null;const d=l?p.createElement(Y1,{base:l},uh(r,c)):uh(r,c);return p.createElement(Ig.Provider,{value:c,children:d})};p.forwardRef((e,t)=>{const n=Gs(),[r,o]=qa(n),{to:s="",href:i=s,onClick:a,asChild:l,children:c,className:d,replace:f,state:h,...x}=e,S=jg(w=>{w.ctrlKey||w.metaKey||w.altKey||w.shiftKey||w.button!==0||(a==null||a(w),w.defaultPrevented||(w.preventDefault(),o(i,e)))}),m=n.hrefs(i[0]==="~"?i.slice(1):n.base+i,n);return l&&p.isValidElement(c)?p.cloneElement(c,{onClick:S,href:m}):p.createElement("a",{...x,onClick:S,href:m,className:d!=null&&d.call?d(r===i):d,children:c,ref:t})});const Fg=e=>Array.isArray(e)?e.flatMap(t=>Fg(t&&t.type===p.Fragment?t.props.children:t)):[e],G1=({children:e,location:t})=>{const n=Gs(),[r]=qa(n);for(const o of Fg(e)){let s=0;if(p.isValidElement(o)&&(s=Lg(n.parser,o.props.path,t||r,o.props.nest))[0])return p.cloneElement(o,{match:s})}return null};var qs=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},xr=typeof window>"u"||"Deno"in globalThis;function pt(){}function q1(e,t){return typeof e=="function"?e(t):e}function Gu(e){return typeof e=="number"&&e>=0&&e!==1/0}function zg(e,t){return Math.max(e+(t||0)-Date.now(),0)}function qr(e,t){return typeof e=="function"?e(t):e}function Tt(e,t){return typeof e=="function"?e(t):e}function ch(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:s,queryKey:i,stale:a}=e;if(i){if(r){if(t.queryHash!==Ed(i,t.options))return!1}else if(!Ms(t.queryKey,i))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||o&&o!==t.state.fetchStatus||s&&!s(t))}function dh(e,t){const{exact:n,status:r,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(Os(t.options.mutationKey)!==Os(s))return!1}else if(!Ms(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function Ed(e,t){return((t==null?void 0:t.queryKeyHashFn)||Os)(e)}function Os(e){return JSON.stringify(e,(t,n)=>Xu(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Ms(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Ms(e[n],t[n])):!1}function $g(e,t){if(e===t)return e;const n=fh(e)&&fh(t);if(n||Xu(e)&&Xu(t)){const r=n?e:Object.keys(e),o=r.length,s=n?t:Object.keys(t),i=s.length,a=n?[]:{};let l=0;for(let c=0;c<i;c++){const d=n?c:s[c];(!n&&r.includes(d)||n)&&e[d]===void 0&&t[d]===void 0?(a[d]=void 0,l++):(a[d]=$g(e[d],t[d]),a[d]===e[d]&&e[d]!==void 0&&l++)}return o===i&&l===o?e:a}return t}function qu(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function fh(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Xu(e){if(!hh(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!hh(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function hh(e){return Object.prototype.toString.call(e)==="[object Object]"}function X1(e){return new Promise(t=>{setTimeout(t,e)})}function Zu(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?$g(e,t):t}function Z1(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function J1(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var bd=Symbol();function Wg(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===bd?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var nr,Cn,no,op,eS=(op=class extends qs{constructor(){super();W(this,nr);W(this,Cn);W(this,no);I(this,no,t=>{if(!xr&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){E(this,Cn)||this.setEventListener(E(this,no))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,Cn))==null||t.call(this),I(this,Cn,void 0))}setEventListener(t){var n;I(this,no,t),(n=E(this,Cn))==null||n.call(this),I(this,Cn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){E(this,nr)!==t&&(I(this,nr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof E(this,nr)=="boolean"?E(this,nr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},nr=new WeakMap,Cn=new WeakMap,no=new WeakMap,op),Nd=new eS,ro,En,oo,sp,tS=(sp=class extends qs{constructor(){super();W(this,ro,!0);W(this,En);W(this,oo);I(this,oo,t=>{if(!xr&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){E(this,En)||this.setEventListener(E(this,oo))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,En))==null||t.call(this),I(this,En,void 0))}setEventListener(t){var n;I(this,oo,t),(n=E(this,En))==null||n.call(this),I(this,En,t(this.setOnline.bind(this)))}setOnline(t){E(this,ro)!==t&&(I(this,ro,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return E(this,ro)}},ro=new WeakMap,En=new WeakMap,oo=new WeakMap,sp),Sa=new tS;function Ju(){let e,t;const n=new Promise((o,s)=>{e=o,t=s});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function nS(e){return Math.min(1e3*2**e,3e4)}function Ug(e){return(e??"online")==="online"?Sa.isOnline():!0}var Bg=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function zl(e){return e instanceof Bg}function Vg(e){let t=!1,n=0,r=!1,o;const s=Ju(),i=m=>{var w;r||(h(new Bg(m)),(w=e.abort)==null||w.call(e))},a=()=>{t=!0},l=()=>{t=!1},c=()=>Nd.isFocused()&&(e.networkMode==="always"||Sa.isOnline())&&e.canRun(),d=()=>Ug(e.networkMode)&&e.canRun(),f=m=>{var w;r||(r=!0,(w=e.onSuccess)==null||w.call(e,m),o==null||o(),s.resolve(m))},h=m=>{var w;r||(r=!0,(w=e.onError)==null||w.call(e,m),o==null||o(),s.reject(m))},x=()=>new Promise(m=>{var w;o=v=>{(r||c())&&m(v)},(w=e.onPause)==null||w.call(e)}).then(()=>{var m;o=void 0,r||(m=e.onContinue)==null||m.call(e)}),S=()=>{if(r)return;let m;const w=n===0?e.initialPromise:void 0;try{m=w??e.fn()}catch(v){m=Promise.reject(v)}Promise.resolve(m).then(f).catch(v=>{var P;if(r)return;const g=e.retry??(xr?0:3),y=e.retryDelay??nS,C=typeof y=="function"?y(n,v):y,b=g===!0||typeof g=="number"&&n<g||typeof g=="function"&&g(n,v);if(t||!b){h(v);return}n++,(P=e.onFail)==null||P.call(e,n,v),X1(C).then(()=>c()?void 0:x()).then(()=>{t?h(v):S()})})};return{promise:s,cancel:i,continue:()=>(o==null||o(),s),cancelRetry:a,continueRetry:l,canStart:d,start:()=>(d()?S():x().then(S),s)}}function rS(){let e=[],t=0,n=a=>{a()},r=a=>{a()},o=a=>setTimeout(a,0);const s=a=>{t?e.push(a):o(()=>{n(a)})},i=()=>{const a=e;e=[],a.length&&o(()=>{r(()=>{a.forEach(l=>{n(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||i()}return l},batchCalls:a=>(...l)=>{s(()=>{a(...l)})},schedule:s,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{o=a}}}var Oe=rS(),rr,ip,Hg=(ip=class{constructor(){W(this,rr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Gu(this.gcTime)&&I(this,rr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(xr?1/0:5*60*1e3))}clearGcTimeout(){E(this,rr)&&(clearTimeout(E(this,rr)),I(this,rr,void 0))}},rr=new WeakMap,ip),so,io,ht,Le,$s,or,Nt,Xt,ap,oS=(ap=class extends Hg{constructor(t){super();W(this,Nt);W(this,so);W(this,io);W(this,ht);W(this,Le);W(this,$s);W(this,or);I(this,or,!1),I(this,$s,t.defaultOptions),this.setOptions(t.options),this.observers=[],I(this,ht,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,I(this,so,sS(this.options)),this.state=t.state??E(this,so),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=E(this,Le))==null?void 0:t.promise}setOptions(t){this.options={...E(this,$s),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&E(this,ht).remove(this)}setData(t,n){const r=Zu(this.state.data,t,this.options);return G(this,Nt,Xt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){G(this,Nt,Xt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=E(this,Le))==null?void 0:r.promise;return(o=E(this,Le))==null||o.cancel(t),n?n.then(pt).catch(pt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(E(this,so))}isActive(){return this.observers.some(t=>Tt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===bd||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!zg(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,Le))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,Le))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),E(this,ht).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(E(this,Le)&&(E(this,or)?E(this,Le).cancel({revert:!0}):E(this,Le).cancelRetry()),this.scheduleGc()),E(this,ht).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||G(this,Nt,Xt).call(this,{type:"invalidate"})}fetch(t,n){var l,c,d;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(E(this,Le))return E(this,Le).continueRetry(),E(this,Le).promise}if(t&&this.setOptions(t),!this.options.queryFn){const f=this.observers.find(h=>h.options.queryFn);f&&this.setOptions(f.options)}const r=new AbortController,o=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(I(this,or,!0),r.signal)})},s=()=>{const f=Wg(this.options,n),h={queryKey:this.queryKey,meta:this.meta};return o(h),I(this,or,!1),this.options.persister?this.options.persister(f,h,this):f(h)},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:s};o(i),(l=this.options.behavior)==null||l.onFetch(i,this),I(this,io,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=i.fetchOptions)==null?void 0:c.meta))&&G(this,Nt,Xt).call(this,{type:"fetch",meta:(d=i.fetchOptions)==null?void 0:d.meta});const a=f=>{var h,x,S,m;zl(f)&&f.silent||G(this,Nt,Xt).call(this,{type:"error",error:f}),zl(f)||((x=(h=E(this,ht).config).onError)==null||x.call(h,f,this),(m=(S=E(this,ht).config).onSettled)==null||m.call(S,this.state.data,f,this)),this.scheduleGc()};return I(this,Le,Vg({initialPromise:n==null?void 0:n.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:f=>{var h,x,S,m;if(f===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(f)}catch(w){a(w);return}(x=(h=E(this,ht).config).onSuccess)==null||x.call(h,f,this),(m=(S=E(this,ht).config).onSettled)==null||m.call(S,f,this.state.error,this),this.scheduleGc()},onError:a,onFail:(f,h)=>{G(this,Nt,Xt).call(this,{type:"failed",failureCount:f,error:h})},onPause:()=>{G(this,Nt,Xt).call(this,{type:"pause"})},onContinue:()=>{G(this,Nt,Xt).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),E(this,Le).start()}},so=new WeakMap,io=new WeakMap,ht=new WeakMap,Le=new WeakMap,$s=new WeakMap,or=new WeakMap,Nt=new WeakSet,Xt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Qg(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return zl(o)&&o.revert&&E(this,io)?{...E(this,io),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Oe.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),E(this,ht).notify({query:this,type:"updated",action:t})})},ap);function Qg(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ug(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function sS(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var zt,lp,iS=(lp=class extends qs{constructor(t={}){super();W(this,zt);this.config=t,I(this,zt,new Map)}build(t,n,r){const o=n.queryKey,s=n.queryHash??Ed(o,n);let i=this.get(s);return i||(i=new oS({cache:this,queryKey:o,queryHash:s,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(i)),i}add(t){E(this,zt).has(t.queryHash)||(E(this,zt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=E(this,zt).get(t.queryHash);n&&(t.destroy(),n===t&&E(this,zt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Oe.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return E(this,zt).get(t)}getAll(){return[...E(this,zt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>ch(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>ch(t,r)):n}notify(t){Oe.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Oe.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Oe.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},zt=new WeakMap,lp),$t,Ve,sr,Wt,gn,up,aS=(up=class extends Hg{constructor(t){super();W(this,Wt);W(this,$t);W(this,Ve);W(this,sr);this.mutationId=t.mutationId,I(this,Ve,t.mutationCache),I(this,$t,[]),this.state=t.state||lS(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){E(this,$t).includes(t)||(E(this,$t).push(t),this.clearGcTimeout(),E(this,Ve).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){I(this,$t,E(this,$t).filter(n=>n!==t)),this.scheduleGc(),E(this,Ve).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){E(this,$t).length||(this.state.status==="pending"?this.scheduleGc():E(this,Ve).remove(this))}continue(){var t;return((t=E(this,sr))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,s,i,a,l,c,d,f,h,x,S,m,w,v,g,y,C,b,P,N;I(this,sr,Vg({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(k,j)=>{G(this,Wt,gn).call(this,{type:"failed",failureCount:k,error:j})},onPause:()=>{G(this,Wt,gn).call(this,{type:"pause"})},onContinue:()=>{G(this,Wt,gn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>E(this,Ve).canRun(this)}));const n=this.state.status==="pending",r=!E(this,sr).canStart();try{if(!n){G(this,Wt,gn).call(this,{type:"pending",variables:t,isPaused:r}),await((s=(o=E(this,Ve).config).onMutate)==null?void 0:s.call(o,t,this));const j=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,t));j!==this.state.context&&G(this,Wt,gn).call(this,{type:"pending",context:j,variables:t,isPaused:r})}const k=await E(this,sr).start();return await((c=(l=E(this,Ve).config).onSuccess)==null?void 0:c.call(l,k,t,this.state.context,this)),await((f=(d=this.options).onSuccess)==null?void 0:f.call(d,k,t,this.state.context)),await((x=(h=E(this,Ve).config).onSettled)==null?void 0:x.call(h,k,null,this.state.variables,this.state.context,this)),await((m=(S=this.options).onSettled)==null?void 0:m.call(S,k,null,t,this.state.context)),G(this,Wt,gn).call(this,{type:"success",data:k}),k}catch(k){try{throw await((v=(w=E(this,Ve).config).onError)==null?void 0:v.call(w,k,t,this.state.context,this)),await((y=(g=this.options).onError)==null?void 0:y.call(g,k,t,this.state.context)),await((b=(C=E(this,Ve).config).onSettled)==null?void 0:b.call(C,void 0,k,this.state.variables,this.state.context,this)),await((N=(P=this.options).onSettled)==null?void 0:N.call(P,void 0,k,t,this.state.context)),k}finally{G(this,Wt,gn).call(this,{type:"error",error:k})}}finally{E(this,Ve).runNext(this)}}},$t=new WeakMap,Ve=new WeakMap,sr=new WeakMap,Wt=new WeakSet,gn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Oe.batch(()=>{E(this,$t).forEach(r=>{r.onMutationUpdate(t)}),E(this,Ve).notify({mutation:this,type:"updated",action:t})})},up);function lS(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var rt,Ws,cp,uS=(cp=class extends qs{constructor(t={}){super();W(this,rt);W(this,Ws);this.config=t,I(this,rt,new Map),I(this,Ws,Date.now())}build(t,n,r){const o=new aS({mutationCache:this,mutationId:++oi(this,Ws)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=Ei(t),r=E(this,rt).get(n)??[];r.push(t),E(this,rt).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Ei(t);if(E(this,rt).has(n)){const o=(r=E(this,rt).get(n))==null?void 0:r.filter(s=>s!==t);o&&(o.length===0?E(this,rt).delete(n):E(this,rt).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=E(this,rt).get(Ei(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=E(this,rt).get(Ei(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Oe.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...E(this,rt).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>dh(n,r))}findAll(t={}){return this.getAll().filter(n=>dh(t,n))}notify(t){Oe.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Oe.batch(()=>Promise.all(t.map(n=>n.continue().catch(pt))))}},rt=new WeakMap,Ws=new WeakMap,cp);function Ei(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function ph(e){return{onFetch:(t,n)=>{var d,f,h,x,S;const r=t.options,o=(h=(f=(d=t.fetchOptions)==null?void 0:d.meta)==null?void 0:f.fetchMore)==null?void 0:h.direction,s=((x=t.state.data)==null?void 0:x.pages)||[],i=((S=t.state.data)==null?void 0:S.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const c=async()=>{let m=!1;const w=y=>{Object.defineProperty(y,"signal",{enumerable:!0,get:()=>(t.signal.aborted?m=!0:t.signal.addEventListener("abort",()=>{m=!0}),t.signal)})},v=Wg(t.options,t.fetchOptions),g=async(y,C,b)=>{if(m)return Promise.reject();if(C==null&&y.pages.length)return Promise.resolve(y);const P={queryKey:t.queryKey,pageParam:C,direction:b?"backward":"forward",meta:t.options.meta};w(P);const N=await v(P),{maxPages:k}=t.options,j=b?J1:Z1;return{pages:j(y.pages,N,k),pageParams:j(y.pageParams,C,k)}};if(o&&s.length){const y=o==="backward",C=y?cS:mh,b={pages:s,pageParams:i},P=C(r,b);a=await g(b,P,y)}else{const y=e??s.length;do{const C=l===0?i[0]??r.initialPageParam:mh(r,a);if(l>0&&C==null)break;a=await g(a,C),l++}while(l<y)}return a};t.options.persister?t.fetchFn=()=>{var m,w;return(w=(m=t.options).persister)==null?void 0:w.call(m,c,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=c}}}function mh(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function cS(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var ye,bn,Nn,ao,lo,Pn,uo,co,dp,dS=(dp=class{constructor(e={}){W(this,ye);W(this,bn);W(this,Nn);W(this,ao);W(this,lo);W(this,Pn);W(this,uo);W(this,co);I(this,ye,e.queryCache||new iS),I(this,bn,e.mutationCache||new uS),I(this,Nn,e.defaultOptions||{}),I(this,ao,new Map),I(this,lo,new Map),I(this,Pn,0)}mount(){oi(this,Pn)._++,E(this,Pn)===1&&(I(this,uo,Nd.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,ye).onFocus())})),I(this,co,Sa.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,ye).onOnline())})))}unmount(){var e,t;oi(this,Pn)._--,E(this,Pn)===0&&((e=E(this,uo))==null||e.call(this),I(this,uo,void 0),(t=E(this,co))==null||t.call(this),I(this,co,void 0))}isFetching(e){return E(this,ye).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return E(this,bn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,ye).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=E(this,ye).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(qr(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return E(this,ye).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=E(this,ye).get(r.queryHash),s=o==null?void 0:o.state.data,i=q1(t,s);if(i!==void 0)return E(this,ye).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return Oe.batch(()=>E(this,ye).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,ye).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=E(this,ye);Oe.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=E(this,ye),r={type:"active",...e};return Oe.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Oe.batch(()=>E(this,ye).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(pt).catch(pt)}invalidateQueries(e={},t={}){return Oe.batch(()=>{if(E(this,ye).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Oe.batch(()=>E(this,ye).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let s=o.fetch(void 0,n);return n.throwOnError||(s=s.catch(pt)),o.state.fetchStatus==="paused"?Promise.resolve():s}));return Promise.all(r).then(pt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=E(this,ye).build(this,t);return n.isStaleByTime(qr(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(pt).catch(pt)}fetchInfiniteQuery(e){return e.behavior=ph(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(pt).catch(pt)}ensureInfiniteQueryData(e){return e.behavior=ph(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Sa.isOnline()?E(this,bn).resumePausedMutations():Promise.resolve()}getQueryCache(){return E(this,ye)}getMutationCache(){return E(this,bn)}getDefaultOptions(){return E(this,Nn)}setDefaultOptions(e){I(this,Nn,e)}setQueryDefaults(e,t){E(this,ao).set(Os(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...E(this,ao).values()];let n={};return t.forEach(r=>{Ms(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){E(this,lo).set(Os(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...E(this,lo).values()];let n={};return t.forEach(r=>{Ms(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...E(this,Nn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Ed(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===bd&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...E(this,Nn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){E(this,ye).clear(),E(this,bn).clear()}},ye=new WeakMap,bn=new WeakMap,Nn=new WeakMap,ao=new WeakMap,lo=new WeakMap,Pn=new WeakMap,uo=new WeakMap,co=new WeakMap,dp),qe,q,Us,He,ir,fo,kn,Ut,Bs,ho,po,ar,lr,Tn,mo,ee,ns,ec,tc,nc,rc,oc,sc,ic,Yg,fp,fS=(fp=class extends qs{constructor(t,n){super();W(this,ee);W(this,qe);W(this,q);W(this,Us);W(this,He);W(this,ir);W(this,fo);W(this,kn);W(this,Ut);W(this,Bs);W(this,ho);W(this,po);W(this,ar);W(this,lr);W(this,Tn);W(this,mo,new Set);this.options=n,I(this,qe,t),I(this,Ut,null),I(this,kn,Ju()),this.options.experimental_prefetchInRender||E(this,kn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(E(this,q).addObserver(this),gh(E(this,q),this.options)?G(this,ee,ns).call(this):this.updateResult(),G(this,ee,rc).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ac(E(this,q),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ac(E(this,q),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,G(this,ee,oc).call(this),G(this,ee,sc).call(this),E(this,q).removeObserver(this)}setOptions(t,n){const r=this.options,o=E(this,q);if(this.options=E(this,qe).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Tt(this.options.enabled,E(this,q))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");G(this,ee,ic).call(this),E(this,q).setOptions(this.options),r._defaulted&&!qu(this.options,r)&&E(this,qe).getQueryCache().notify({type:"observerOptionsUpdated",query:E(this,q),observer:this});const s=this.hasListeners();s&&vh(E(this,q),o,this.options,r)&&G(this,ee,ns).call(this),this.updateResult(n),s&&(E(this,q)!==o||Tt(this.options.enabled,E(this,q))!==Tt(r.enabled,E(this,q))||qr(this.options.staleTime,E(this,q))!==qr(r.staleTime,E(this,q)))&&G(this,ee,ec).call(this);const i=G(this,ee,tc).call(this);s&&(E(this,q)!==o||Tt(this.options.enabled,E(this,q))!==Tt(r.enabled,E(this,q))||i!==E(this,Tn))&&G(this,ee,nc).call(this,i)}getOptimisticResult(t){const n=E(this,qe).getQueryCache().build(E(this,qe),t),r=this.createResult(n,t);return pS(this,r)&&(I(this,He,r),I(this,fo,this.options),I(this,ir,E(this,q).state)),r}getCurrentResult(){return E(this,He)}trackResult(t,n){const r={};return Object.keys(t).forEach(o=>{Object.defineProperty(r,o,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(o),n==null||n(o),t[o])})}),r}trackProp(t){E(this,mo).add(t)}getCurrentQuery(){return E(this,q)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=E(this,qe).defaultQueryOptions(t),r=E(this,qe).getQueryCache().build(E(this,qe),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){return G(this,ee,ns).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),E(this,He)))}createResult(t,n){var k;const r=E(this,q),o=this.options,s=E(this,He),i=E(this,ir),a=E(this,fo),c=t!==r?t.state:E(this,Us),{state:d}=t;let f={...d},h=!1,x;if(n._optimisticResults){const j=this.hasListeners(),O=!j&&gh(t,n),F=j&&vh(t,r,n,o);(O||F)&&(f={...f,...Qg(d.data,t.options)}),n._optimisticResults==="isRestoring"&&(f.fetchStatus="idle")}let{error:S,errorUpdatedAt:m,status:w}=f;if(n.select&&f.data!==void 0)if(s&&f.data===(i==null?void 0:i.data)&&n.select===E(this,Bs))x=E(this,ho);else try{I(this,Bs,n.select),x=n.select(f.data),x=Zu(s==null?void 0:s.data,x,n),I(this,ho,x),I(this,Ut,null)}catch(j){I(this,Ut,j)}else x=f.data;if(n.placeholderData!==void 0&&x===void 0&&w==="pending"){let j;if(s!=null&&s.isPlaceholderData&&n.placeholderData===(a==null?void 0:a.placeholderData))j=s.data;else if(j=typeof n.placeholderData=="function"?n.placeholderData((k=E(this,po))==null?void 0:k.state.data,E(this,po)):n.placeholderData,n.select&&j!==void 0)try{j=n.select(j),I(this,Ut,null)}catch(O){I(this,Ut,O)}j!==void 0&&(w="success",x=Zu(s==null?void 0:s.data,j,n),h=!0)}E(this,Ut)&&(S=E(this,Ut),x=E(this,ho),m=Date.now(),w="error");const v=f.fetchStatus==="fetching",g=w==="pending",y=w==="error",C=g&&v,b=x!==void 0,N={status:w,fetchStatus:f.fetchStatus,isPending:g,isSuccess:w==="success",isError:y,isInitialLoading:C,isLoading:C,data:x,dataUpdatedAt:f.dataUpdatedAt,error:S,errorUpdatedAt:m,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>c.dataUpdateCount||f.errorUpdateCount>c.errorUpdateCount,isFetching:v,isRefetching:v&&!g,isLoadingError:y&&!b,isPaused:f.fetchStatus==="paused",isPlaceholderData:h,isRefetchError:y&&b,isStale:Pd(t,n),refetch:this.refetch,promise:E(this,kn)};if(this.options.experimental_prefetchInRender){const j=A=>{N.status==="error"?A.reject(N.error):N.data!==void 0&&A.resolve(N.data)},O=()=>{const A=I(this,kn,N.promise=Ju());j(A)},F=E(this,kn);switch(F.status){case"pending":t.queryHash===r.queryHash&&j(F);break;case"fulfilled":(N.status==="error"||N.data!==F.value)&&O();break;case"rejected":(N.status!=="error"||N.error!==F.reason)&&O();break}}return N}updateResult(t){const n=E(this,He),r=this.createResult(E(this,q),this.options);if(I(this,ir,E(this,q).state),I(this,fo,this.options),E(this,ir).data!==void 0&&I(this,po,E(this,q)),qu(r,n))return;I(this,He,r);const o={},s=()=>{if(!n)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!E(this,mo).size)return!0;const l=new Set(a??E(this,mo));return this.options.throwOnError&&l.add("error"),Object.keys(E(this,He)).some(c=>{const d=c;return E(this,He)[d]!==n[d]&&l.has(d)})};(t==null?void 0:t.listeners)!==!1&&s()&&(o.listeners=!0),G(this,ee,Yg).call(this,{...o,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&G(this,ee,rc).call(this)}},qe=new WeakMap,q=new WeakMap,Us=new WeakMap,He=new WeakMap,ir=new WeakMap,fo=new WeakMap,kn=new WeakMap,Ut=new WeakMap,Bs=new WeakMap,ho=new WeakMap,po=new WeakMap,ar=new WeakMap,lr=new WeakMap,Tn=new WeakMap,mo=new WeakMap,ee=new WeakSet,ns=function(t){G(this,ee,ic).call(this);let n=E(this,q).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(pt)),n},ec=function(){G(this,ee,oc).call(this);const t=qr(this.options.staleTime,E(this,q));if(xr||E(this,He).isStale||!Gu(t))return;const r=zg(E(this,He).dataUpdatedAt,t)+1;I(this,ar,setTimeout(()=>{E(this,He).isStale||this.updateResult()},r))},tc=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(E(this,q)):this.options.refetchInterval)??!1},nc=function(t){G(this,ee,sc).call(this),I(this,Tn,t),!(xr||Tt(this.options.enabled,E(this,q))===!1||!Gu(E(this,Tn))||E(this,Tn)===0)&&I(this,lr,setInterval(()=>{(this.options.refetchIntervalInBackground||Nd.isFocused())&&G(this,ee,ns).call(this)},E(this,Tn)))},rc=function(){G(this,ee,ec).call(this),G(this,ee,nc).call(this,G(this,ee,tc).call(this))},oc=function(){E(this,ar)&&(clearTimeout(E(this,ar)),I(this,ar,void 0))},sc=function(){E(this,lr)&&(clearInterval(E(this,lr)),I(this,lr,void 0))},ic=function(){const t=E(this,qe).getQueryCache().build(E(this,qe),this.options);if(t===E(this,q))return;const n=E(this,q);I(this,q,t),I(this,Us,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},Yg=function(t){Oe.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(E(this,He))}),E(this,qe).getQueryCache().notify({query:E(this,q),type:"observerResultsUpdated"})})},fp);function hS(e,t){return Tt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function gh(e,t){return hS(e,t)||e.state.data!==void 0&&ac(e,t,t.refetchOnMount)}function ac(e,t,n){if(Tt(t.enabled,e)!==!1){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&Pd(e,t)}return!1}function vh(e,t,n,r){return(e!==t||Tt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&Pd(e,n)}function Pd(e,t){return Tt(t.enabled,e)!==!1&&e.isStaleByTime(qr(t.staleTime,e))}function pS(e,t){return!qu(e.getCurrentResult(),t)}var Kg=p.createContext(void 0),mS=e=>{const t=p.useContext(Kg);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},gS=({client:e,children:t})=>(p.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),u.jsx(Kg.Provider,{value:e,children:t})),Gg=p.createContext(!1),vS=()=>p.useContext(Gg);Gg.Provider;function yS(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var xS=p.createContext(yS()),wS=()=>p.useContext(xS);function SS(e,t){return typeof e=="function"?e(...t):!!e}function yh(){}var CS=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},ES=e=>{p.useEffect(()=>{e.clearReset()},[e])},bS=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&SS(n,[e.error,r]),NS=e=>{e.suspense&&(e.staleTime===void 0&&(e.staleTime=1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},PS=(e,t)=>e.isLoading&&e.isFetching&&!t,kS=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,xh=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function TS(e,t,n){var d,f,h,x,S;const r=mS(),o=vS(),s=wS(),i=r.defaultQueryOptions(e);(f=(d=r.getDefaultOptions().queries)==null?void 0:d._experimental_beforeQuery)==null||f.call(d,i),i._optimisticResults=o?"isRestoring":"optimistic",NS(i),CS(i,s),ES(s);const a=!r.getQueryCache().get(i.queryHash),[l]=p.useState(()=>new t(r,i)),c=l.getOptimisticResult(i);if(p.useSyncExternalStore(p.useCallback(m=>{const w=o?yh:l.subscribe(Oe.batchCalls(m));return l.updateResult(),w},[l,o]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),p.useEffect(()=>{l.setOptions(i,{listeners:!1})},[i,l]),kS(i,c))throw xh(i,l,s);if(bS({result:c,errorResetBoundary:s,throwOnError:i.throwOnError,query:r.getQueryCache().get(i.queryHash)}))throw c.error;if((x=(h=r.getDefaultOptions().queries)==null?void 0:h._experimental_afterQuery)==null||x.call(h,i,c),i.experimental_prefetchInRender&&!xr&&PS(c,o)){const m=a?xh(i,l,s):(S=r.getQueryCache().get(i.queryHash))==null?void 0:S.promise;m==null||m.catch(yh).finally(()=>{l.updateResult()})}return i.notifyOnChangeProps?c:l.trackResult(c)}function jo(e,t){return TS(e,fS)}async function qg(e){if(!e.ok){const t=await e.text()||e.statusText;throw new Error(`${e.status}: ${t}`)}}async function Xr(e,t,n){const r=await fetch(t,{method:e,headers:n?{"Content-Type":"application/json"}:{},body:n?JSON.stringify(n):void 0,credentials:"include"});return await qg(r),r}const RS=({on401:e})=>async({queryKey:t})=>{const n=await fetch(t[0],{credentials:"include"});return e==="returnNull"&&n.status===401?null:(await qg(n),await n.json())},Ca=new dS({defaultOptions:{queries:{queryFn:RS({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),jS=1,OS=1e6;let $l=0;function MS(){return $l=($l+1)%Number.MAX_SAFE_INTEGER,$l.toString()}const Wl=new Map,wh=e=>{if(Wl.has(e))return;const t=setTimeout(()=>{Wl.delete(e),hs({type:"REMOVE_TOAST",toastId:e})},OS);Wl.set(e,t)},_S=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,jS)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?wh(n):e.toasts.forEach(r=>{wh(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},Vi=[];let Hi={toasts:[]};function hs(e){Hi=_S(Hi,e),Vi.forEach(t=>{t(Hi)})}function AS({...e}){const t=MS(),n=o=>hs({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>hs({type:"DISMISS_TOAST",toastId:t});return hs({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Xa(){const[e,t]=p.useState(Hi);return p.useEffect(()=>(Vi.push(t),()=>{const n=Vi.indexOf(t);n>-1&&Vi.splice(n,1)}),[e]),{...e,toast:AS,dismiss:n=>hs({type:"DISMISS_TOAST",toastId:n})}}function Q(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Sh(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Xg(...e){return t=>{let n=!1;const r=e.map(o=>{const s=Sh(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():Sh(e[o],null)}}}}function he(...e){return p.useCallback(Xg(...e),e)}function DS(e,t){const n=p.createContext(t),r=s=>{const{children:i,...a}=s,l=p.useMemo(()=>a,Object.values(a));return u.jsx(n.Provider,{value:l,children:i})};r.displayName=e+"Provider";function o(s){const i=p.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function Oo(e,t=[]){let n=[];function r(s,i){const a=p.createContext(i),l=n.length;n=[...n,i];const c=f=>{var v;const{scope:h,children:x,...S}=f,m=((v=h==null?void 0:h[e])==null?void 0:v[l])||a,w=p.useMemo(()=>S,Object.values(S));return u.jsx(m.Provider,{value:w,children:x})};c.displayName=s+"Provider";function d(f,h){var m;const x=((m=h==null?void 0:h[e])==null?void 0:m[l])||a,S=p.useContext(x);if(S)return S;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return[c,d]}const o=()=>{const s=n.map(i=>p.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return p.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,IS(o,...t)]}function IS(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:c})=>{const f=l(s)[`__scope${c}`];return{...a,...f}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function bo(e){const t=FS(e),n=p.forwardRef((r,o)=>{const{children:s,...i}=r,a=p.Children.toArray(s),l=a.find($S);if(l){const c=l.props.children,d=a.map(f=>f===l?p.Children.count(c)>1?p.Children.only(null):p.isValidElement(c)?c.props.children:null:f);return u.jsx(t,{...i,ref:o,children:p.isValidElement(c)?p.cloneElement(c,void 0,d):null})}return u.jsx(t,{...i,ref:o,children:s})});return n.displayName=`${e}.Slot`,n}var LS=bo("Slot");function FS(e){const t=p.forwardRef((n,r)=>{const{children:o,...s}=n;if(p.isValidElement(o)){const i=US(o),a=WS(s,o.props);return o.type!==p.Fragment&&(a.ref=r?Xg(r,i):i),p.cloneElement(o,a)}return p.Children.count(o)>1?p.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Zg=Symbol("radix.slottable");function zS(e){const t=({children:n})=>u.jsx(u.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Zg,t}function $S(e){return p.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Zg}function WS(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function US(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Jg(e){const t=e+"CollectionProvider",[n,r]=Oo(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=m=>{const{scope:w,children:v}=m,g=mn.useRef(null),y=mn.useRef(new Map).current;return u.jsx(o,{scope:w,itemMap:y,collectionRef:g,children:v})};i.displayName=t;const a=e+"CollectionSlot",l=bo(a),c=mn.forwardRef((m,w)=>{const{scope:v,children:g}=m,y=s(a,v),C=he(w,y.collectionRef);return u.jsx(l,{ref:C,children:g})});c.displayName=a;const d=e+"CollectionItemSlot",f="data-radix-collection-item",h=bo(d),x=mn.forwardRef((m,w)=>{const{scope:v,children:g,...y}=m,C=mn.useRef(null),b=he(w,C),P=s(d,v);return mn.useEffect(()=>(P.itemMap.set(C,{ref:C,...y}),()=>void P.itemMap.delete(C))),u.jsx(h,{[f]:"",ref:b,children:g})});x.displayName=d;function S(m){const w=s(e+"CollectionConsumer",m);return mn.useCallback(()=>{const g=w.collectionRef.current;if(!g)return[];const y=Array.from(g.querySelectorAll(`[${f}]`));return Array.from(w.itemMap.values()).sort((P,N)=>y.indexOf(P.ref.current)-y.indexOf(N.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:i,Slot:c,ItemSlot:x},S,r]}var BS=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],J=BS.reduce((e,t)=>{const n=bo(`Primitive.${t}`),r=p.forwardRef((o,s)=>{const{asChild:i,...a}=o,l=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(l,{...a,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function ev(e,t){e&&Nr.flushSync(()=>e.dispatchEvent(t))}function ct(e){const t=p.useRef(e);return p.useEffect(()=>{t.current=e}),p.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function VS(e,t=globalThis==null?void 0:globalThis.document){const n=ct(e);p.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var HS="DismissableLayer",lc="dismissableLayer.update",QS="dismissableLayer.pointerDownOutside",YS="dismissableLayer.focusOutside",Ch,tv=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Xs=p.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,c=p.useContext(tv),[d,f]=p.useState(null),h=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,x]=p.useState({}),S=he(t,N=>f(N)),m=Array.from(c.layers),[w]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),v=m.indexOf(w),g=d?m.indexOf(d):-1,y=c.layersWithOutsidePointerEventsDisabled.size>0,C=g>=v,b=GS(N=>{const k=N.target,j=[...c.branches].some(O=>O.contains(k));!C||j||(o==null||o(N),i==null||i(N),N.defaultPrevented||a==null||a())},h),P=qS(N=>{const k=N.target;[...c.branches].some(O=>O.contains(k))||(s==null||s(N),i==null||i(N),N.defaultPrevented||a==null||a())},h);return VS(N=>{g===c.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&a&&(N.preventDefault(),a()))},h),p.useEffect(()=>{if(d)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(Ch=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),Eh(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=Ch)}},[d,h,n,c]),p.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),Eh())},[d,c]),p.useEffect(()=>{const N=()=>x({});return document.addEventListener(lc,N),()=>document.removeEventListener(lc,N)},[]),u.jsx(J.div,{...l,ref:S,style:{pointerEvents:y?C?"auto":"none":void 0,...e.style},onFocusCapture:Q(e.onFocusCapture,P.onFocusCapture),onBlurCapture:Q(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:Q(e.onPointerDownCapture,b.onPointerDownCapture)})});Xs.displayName=HS;var KS="DismissableLayerBranch",nv=p.forwardRef((e,t)=>{const n=p.useContext(tv),r=p.useRef(null),o=he(t,r);return p.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),u.jsx(J.div,{...e,ref:o})});nv.displayName=KS;function GS(e,t=globalThis==null?void 0:globalThis.document){const n=ct(e),r=p.useRef(!1),o=p.useRef(()=>{});return p.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let l=function(){rv(QS,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function qS(e,t=globalThis==null?void 0:globalThis.document){const n=ct(e),r=p.useRef(!1);return p.useEffect(()=>{const o=s=>{s.target&&!r.current&&rv(YS,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Eh(){const e=new CustomEvent(lc);document.dispatchEvent(e)}function rv(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ev(o,s):o.dispatchEvent(s)}var XS=Xs,ZS=nv,We=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},JS="Portal",Za=p.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=p.useState(!1);We(()=>s(!0),[]);const i=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?x1.createPortal(u.jsx(J.div,{...r,ref:t}),i):null});Za.displayName=JS;function eC(e,t){return p.useReducer((n,r)=>t[n][r]??n,e)}var Mo=e=>{const{present:t,children:n}=e,r=tC(t),o=typeof n=="function"?n({present:r.isPresent}):p.Children.only(n),s=he(r.ref,nC(o));return typeof n=="function"||r.isPresent?p.cloneElement(o,{ref:s}):null};Mo.displayName="Presence";function tC(e){const[t,n]=p.useState(),r=p.useRef({}),o=p.useRef(e),s=p.useRef("none"),i=e?"mounted":"unmounted",[a,l]=eC(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const c=bi(r.current);s.current=a==="mounted"?c:"none"},[a]),We(()=>{const c=r.current,d=o.current;if(d!==e){const h=s.current,x=bi(c);e?l("MOUNT"):x==="none"||(c==null?void 0:c.display)==="none"?l("UNMOUNT"):l(d&&h!==x?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),We(()=>{if(t){let c;const d=t.ownerDocument.defaultView??window,f=x=>{const m=bi(r.current).includes(x.animationName);if(x.target===t&&m&&(l("ANIMATION_END"),!o.current)){const w=t.style.animationFillMode;t.style.animationFillMode="forwards",c=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=w)})}},h=x=>{x.target===t&&(s.current=bi(r.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(c),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:p.useCallback(c=>{c&&(r.current=getComputedStyle(c)),n(c)},[])}}function bi(e){return(e==null?void 0:e.animationName)||"none"}function nC(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Ea({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=rC({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,a=ct(n),l=p.useCallback(c=>{if(s){const f=typeof c=="function"?c(e):c;f!==e&&a(f)}else o(c)},[s,e,o,a]);return[i,l]}function rC({defaultProp:e,onChange:t}){const n=p.useState(e),[r]=n,o=p.useRef(r),s=ct(t);return p.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var oC="VisuallyHidden",Zs=p.forwardRef((e,t)=>u.jsx(J.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Zs.displayName=oC;var sC=Zs,kd="ToastProvider",[Td,iC,aC]=Jg("Toast"),[ov,h2]=Oo("Toast",[aC]),[lC,Ja]=ov(kd),sv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[a,l]=p.useState(null),[c,d]=p.useState(0),f=p.useRef(!1),h=p.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${kd}\`. Expected non-empty \`string\`.`),u.jsx(Td.Provider,{scope:t,children:u.jsx(lC,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:c,viewport:a,onViewportChange:l,onToastAdd:p.useCallback(()=>d(x=>x+1),[]),onToastRemove:p.useCallback(()=>d(x=>x-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:h,children:i})})};sv.displayName=kd;var iv="ToastViewport",uC=["F8"],uc="toast.viewportPause",cc="toast.viewportResume",av=p.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=uC,label:o="Notifications ({hotkey})",...s}=e,i=Ja(iv,n),a=iC(n),l=p.useRef(null),c=p.useRef(null),d=p.useRef(null),f=p.useRef(null),h=he(t,f,i.onViewportChange),x=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),S=i.toastCount>0;p.useEffect(()=>{const w=v=>{var y;r.length!==0&&r.every(C=>v[C]||v.code===C)&&((y=f.current)==null||y.focus())};return document.addEventListener("keydown",w),()=>document.removeEventListener("keydown",w)},[r]),p.useEffect(()=>{const w=l.current,v=f.current;if(S&&w&&v){const g=()=>{if(!i.isClosePausedRef.current){const P=new CustomEvent(uc);v.dispatchEvent(P),i.isClosePausedRef.current=!0}},y=()=>{if(i.isClosePausedRef.current){const P=new CustomEvent(cc);v.dispatchEvent(P),i.isClosePausedRef.current=!1}},C=P=>{!w.contains(P.relatedTarget)&&y()},b=()=>{w.contains(document.activeElement)||y()};return w.addEventListener("focusin",g),w.addEventListener("focusout",C),w.addEventListener("pointermove",g),w.addEventListener("pointerleave",b),window.addEventListener("blur",g),window.addEventListener("focus",y),()=>{w.removeEventListener("focusin",g),w.removeEventListener("focusout",C),w.removeEventListener("pointermove",g),w.removeEventListener("pointerleave",b),window.removeEventListener("blur",g),window.removeEventListener("focus",y)}}},[S,i.isClosePausedRef]);const m=p.useCallback(({tabbingDirection:w})=>{const g=a().map(y=>{const C=y.ref.current,b=[C,...CC(C)];return w==="forwards"?b:b.reverse()});return(w==="forwards"?g.reverse():g).flat()},[a]);return p.useEffect(()=>{const w=f.current;if(w){const v=g=>{var b,P,N;const y=g.altKey||g.ctrlKey||g.metaKey;if(g.key==="Tab"&&!y){const k=document.activeElement,j=g.shiftKey;if(g.target===w&&j){(b=c.current)==null||b.focus();return}const A=m({tabbingDirection:j?"backwards":"forwards"}),B=A.findIndex(_=>_===k);Ul(A.slice(B+1))?g.preventDefault():j?(P=c.current)==null||P.focus():(N=d.current)==null||N.focus()}};return w.addEventListener("keydown",v),()=>w.removeEventListener("keydown",v)}},[a,m]),u.jsxs(ZS,{ref:l,role:"region","aria-label":o.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:S?void 0:"none"},children:[S&&u.jsx(dc,{ref:c,onFocusFromOutsideViewport:()=>{const w=m({tabbingDirection:"forwards"});Ul(w)}}),u.jsx(Td.Slot,{scope:n,children:u.jsx(J.ol,{tabIndex:-1,...s,ref:h})}),S&&u.jsx(dc,{ref:d,onFocusFromOutsideViewport:()=>{const w=m({tabbingDirection:"backwards"});Ul(w)}})]})});av.displayName=iv;var lv="ToastFocusProxy",dc=p.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=Ja(lv,n);return u.jsx(Zs,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var c;const a=i.relatedTarget;!((c=s.viewport)!=null&&c.contains(a))&&r()}})});dc.displayName=lv;var el="Toast",cC="toast.swipeStart",dC="toast.swipeMove",fC="toast.swipeCancel",hC="toast.swipeEnd",uv=p.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[a=!0,l]=Ea({prop:r,defaultProp:o,onChange:s});return u.jsx(Mo,{present:n||a,children:u.jsx(gC,{open:a,...i,ref:t,onClose:()=>l(!1),onPause:ct(e.onPause),onResume:ct(e.onResume),onSwipeStart:Q(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Q(e.onSwipeMove,c=>{const{x:d,y:f}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${f}px`)}),onSwipeCancel:Q(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Q(e.onSwipeEnd,c=>{const{x:d,y:f}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${f}px`),l(!1)})})})});uv.displayName=el;var[pC,mC]=ov(el,{onClose(){}}),gC=p.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:a,onPause:l,onResume:c,onSwipeStart:d,onSwipeMove:f,onSwipeCancel:h,onSwipeEnd:x,...S}=e,m=Ja(el,n),[w,v]=p.useState(null),g=he(t,_=>v(_)),y=p.useRef(null),C=p.useRef(null),b=o||m.duration,P=p.useRef(0),N=p.useRef(b),k=p.useRef(0),{onToastAdd:j,onToastRemove:O}=m,F=ct(()=>{var Y;(w==null?void 0:w.contains(document.activeElement))&&((Y=m.viewport)==null||Y.focus()),i()}),A=p.useCallback(_=>{!_||_===1/0||(window.clearTimeout(k.current),P.current=new Date().getTime(),k.current=window.setTimeout(F,_))},[F]);p.useEffect(()=>{const _=m.viewport;if(_){const Y=()=>{A(N.current),c==null||c()},z=()=>{const V=new Date().getTime()-P.current;N.current=N.current-V,window.clearTimeout(k.current),l==null||l()};return _.addEventListener(uc,z),_.addEventListener(cc,Y),()=>{_.removeEventListener(uc,z),_.removeEventListener(cc,Y)}}},[m.viewport,b,l,c,A]),p.useEffect(()=>{s&&!m.isClosePausedRef.current&&A(b)},[s,b,m.isClosePausedRef,A]),p.useEffect(()=>(j(),()=>O()),[j,O]);const B=p.useMemo(()=>w?gv(w):null,[w]);return m.viewport?u.jsxs(u.Fragment,{children:[B&&u.jsx(vC,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:B}),u.jsx(pC,{scope:n,onClose:F,children:Nr.createPortal(u.jsx(Td.ItemSlot,{scope:n,children:u.jsx(XS,{asChild:!0,onEscapeKeyDown:Q(a,()=>{m.isFocusedToastEscapeKeyDownRef.current||F(),m.isFocusedToastEscapeKeyDownRef.current=!1}),children:u.jsx(J.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":m.swipeDirection,...S,ref:g,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Q(e.onKeyDown,_=>{_.key==="Escape"&&(a==null||a(_.nativeEvent),_.nativeEvent.defaultPrevented||(m.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:Q(e.onPointerDown,_=>{_.button===0&&(y.current={x:_.clientX,y:_.clientY})}),onPointerMove:Q(e.onPointerMove,_=>{if(!y.current)return;const Y=_.clientX-y.current.x,z=_.clientY-y.current.y,V=!!C.current,T=["left","right"].includes(m.swipeDirection),M=["left","up"].includes(m.swipeDirection)?Math.min:Math.max,L=T?M(0,Y):0,U=T?0:M(0,z),re=_.pointerType==="touch"?10:2,Ue={x:L,y:U},Ee={originalEvent:_,delta:Ue};V?(C.current=Ue,Ni(dC,f,Ee,{discrete:!1})):bh(Ue,m.swipeDirection,re)?(C.current=Ue,Ni(cC,d,Ee,{discrete:!1}),_.target.setPointerCapture(_.pointerId)):(Math.abs(Y)>re||Math.abs(z)>re)&&(y.current=null)}),onPointerUp:Q(e.onPointerUp,_=>{const Y=C.current,z=_.target;if(z.hasPointerCapture(_.pointerId)&&z.releasePointerCapture(_.pointerId),C.current=null,y.current=null,Y){const V=_.currentTarget,T={originalEvent:_,delta:Y};bh(Y,m.swipeDirection,m.swipeThreshold)?Ni(hC,x,T,{discrete:!0}):Ni(fC,h,T,{discrete:!0}),V.addEventListener("click",M=>M.preventDefault(),{once:!0})}})})})}),m.viewport)})]}):null}),vC=e=>{const{__scopeToast:t,children:n,...r}=e,o=Ja(el,t),[s,i]=p.useState(!1),[a,l]=p.useState(!1);return wC(()=>i(!0)),p.useEffect(()=>{const c=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(c)},[]),a?null:u.jsx(Za,{asChild:!0,children:u.jsx(Zs,{...r,children:s&&u.jsxs(u.Fragment,{children:[o.label," ",n]})})})},yC="ToastTitle",cv=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return u.jsx(J.div,{...r,ref:t})});cv.displayName=yC;var xC="ToastDescription",dv=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return u.jsx(J.div,{...r,ref:t})});dv.displayName=xC;var fv="ToastAction",hv=p.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?u.jsx(mv,{altText:n,asChild:!0,children:u.jsx(Rd,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${fv}\`. Expected non-empty \`string\`.`),null)});hv.displayName=fv;var pv="ToastClose",Rd=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=mC(pv,n);return u.jsx(mv,{asChild:!0,children:u.jsx(J.button,{type:"button",...r,ref:t,onClick:Q(e.onClick,o.onClose)})})});Rd.displayName=pv;var mv=p.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return u.jsx(J.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function gv(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),SC(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...gv(r))}}),t}function Ni(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ev(o,s):o.dispatchEvent(s)}var bh=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function wC(e=()=>{}){const t=ct(e);We(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function SC(e){return e.nodeType===e.ELEMENT_NODE}function CC(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ul(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var EC=sv,vv=av,yv=uv,xv=cv,wv=dv,Sv=hv,Cv=Rd;function Ev(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Ev(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function bv(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Ev(e))&&(r&&(r+=" "),r+=t);return r}const Nh=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ph=bv,Js=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Ph(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(c=>{const d=n==null?void 0:n[c],f=s==null?void 0:s[c];if(d===null)return null;const h=Nh(d)||Nh(f);return o[c][h]}),a=n&&Object.entries(n).reduce((c,d)=>{let[f,h]=d;return h===void 0||(c[f]=h),c},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,d)=>{let{class:f,className:h,...x}=d;return Object.entries(x).every(S=>{let[m,w]=S;return Array.isArray(w)?w.includes({...s,...a}[m]):{...s,...a}[m]===w})?[...c,f,h]:c},[]);return Ph(e,i,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bC=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Nv=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var NC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PC=p.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...a},l)=>p.createElement("svg",{ref:l,...NC,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Nv("lucide",o),...a},[...i.map(([c,d])=>p.createElement(c,d)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=(e,t)=>{const n=p.forwardRef(({className:r,...o},s)=>p.createElement(PC,{ref:s,iconNode:t,className:Nv(`lucide-${bC(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ba=Se("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kC=Se("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TC=Se("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pv=Se("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RC=Se("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qi=Se("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Or=Se("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _s=Se("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jC=Se("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OC=Se("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MC=Se("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _C=Se("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kv=Se("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=Se("Reply",[["polyline",{points:"9 17 4 12 9 7",key:"hvgpf2"}],["path",{d:"M20 18v-2a4 4 0 0 0-4-4H4",key:"5vmcpk"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AC=Se("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vl=Se("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DC=Se("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const As=Se("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IC=Se("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tv=Se("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rv=Se("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),jd="-",LC=e=>{const t=zC(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(jd);return a[0]===""&&a.length!==1&&a.shift(),jv(a,t)||FC(i)},getConflictingClassGroupIds:(i,a)=>{const l=n[i]||[];return a&&r[i]?[...l,...r[i]]:l}}},jv=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?jv(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(jd);return(i=t.validators.find(({validator:a})=>a(s)))==null?void 0:i.classGroupId},kh=/^\[(.+)\]$/,FC=e=>{if(kh.test(e)){const t=kh.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},zC=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return WC(Object.entries(e.classGroups),n).forEach(([s,i])=>{fc(i,r,s,t)}),r},fc=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Th(t,o);s.classGroupId=n;return}if(typeof o=="function"){if($C(o)){fc(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{fc(i,Th(t,s),n,r)})})},Th=(e,t)=>{let n=e;return t.split(jd).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},$C=e=>e.isThemeGetter,WC=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,a])=>[t+i,a])):s);return[n,o]}):e,UC=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Ov="!",BC=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=a=>{const l=[];let c=0,d=0,f;for(let w=0;w<a.length;w++){let v=a[w];if(c===0){if(v===o&&(r||a.slice(w,w+s)===t)){l.push(a.slice(d,w)),d=w+s;continue}if(v==="/"){f=w;continue}}v==="["?c++:v==="]"&&c--}const h=l.length===0?a:a.substring(d),x=h.startsWith(Ov),S=x?h.substring(1):h,m=f&&f>d?f-d:void 0;return{modifiers:l,hasImportantModifier:x,baseClassName:S,maybePostfixModifierPosition:m}};return n?a=>n({className:a,parseClassName:i}):i},VC=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},HC=e=>({cache:UC(e.cacheSize),parseClassName:BC(e),...LC(e)}),QC=/\s+/,YC=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(QC);let a="";for(let l=i.length-1;l>=0;l-=1){const c=i[l],{modifiers:d,hasImportantModifier:f,baseClassName:h,maybePostfixModifierPosition:x}=n(c);let S=!!x,m=r(S?h.substring(0,x):h);if(!m){if(!S){a=c+(a.length>0?" "+a:a);continue}if(m=r(h),!m){a=c+(a.length>0?" "+a:a);continue}S=!1}const w=VC(d).join(":"),v=f?w+Ov:w,g=v+m;if(s.includes(g))continue;s.push(g);const y=o(m,S);for(let C=0;C<y.length;++C){const b=y[C];s.push(v+b)}a=c+(a.length>0?" "+a:a)}return a};function KC(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Mv(t))&&(r&&(r+=" "),r+=n);return r}const Mv=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Mv(e[r]))&&(n&&(n+=" "),n+=t);return n};function GC(e,...t){let n,r,o,s=i;function i(l){const c=t.reduce((d,f)=>f(d),e());return n=HC(c),r=n.cache.get,o=n.cache.set,s=a,a(l)}function a(l){const c=r(l);if(c)return c;const d=YC(l,n);return o(l,d),d}return function(){return s(KC.apply(null,arguments))}}const ce=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},_v=/^\[(?:([a-z-]+):)?(.+)\]$/i,qC=/^\d+\/\d+$/,XC=new Set(["px","full","screen"]),ZC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,JC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,eE=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,tE=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,nE=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,qt=e=>Zr(e)||XC.has(e)||qC.test(e),fn=e=>_o(e,"length",cE),Zr=e=>!!e&&!Number.isNaN(Number(e)),Hl=e=>_o(e,"number",Zr),Ko=e=>!!e&&Number.isInteger(Number(e)),rE=e=>e.endsWith("%")&&Zr(e.slice(0,-1)),H=e=>_v.test(e),hn=e=>ZC.test(e),oE=new Set(["length","size","percentage"]),sE=e=>_o(e,oE,Av),iE=e=>_o(e,"position",Av),aE=new Set(["image","url"]),lE=e=>_o(e,aE,fE),uE=e=>_o(e,"",dE),Go=()=>!0,_o=(e,t,n)=>{const r=_v.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},cE=e=>JC.test(e)&&!eE.test(e),Av=()=>!1,dE=e=>tE.test(e),fE=e=>nE.test(e),hE=()=>{const e=ce("colors"),t=ce("spacing"),n=ce("blur"),r=ce("brightness"),o=ce("borderColor"),s=ce("borderRadius"),i=ce("borderSpacing"),a=ce("borderWidth"),l=ce("contrast"),c=ce("grayscale"),d=ce("hueRotate"),f=ce("invert"),h=ce("gap"),x=ce("gradientColorStops"),S=ce("gradientColorStopPositions"),m=ce("inset"),w=ce("margin"),v=ce("opacity"),g=ce("padding"),y=ce("saturate"),C=ce("scale"),b=ce("sepia"),P=ce("skew"),N=ce("space"),k=ce("translate"),j=()=>["auto","contain","none"],O=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto",H,t],A=()=>[H,t],B=()=>["",qt,fn],_=()=>["auto",Zr,H],Y=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],z=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],T=()=>["start","end","center","between","around","evenly","stretch"],M=()=>["","0",H],L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],U=()=>[Zr,H];return{cacheSize:500,separator:":",theme:{colors:[Go],spacing:[qt,fn],blur:["none","",hn,H],brightness:U(),borderColor:[e],borderRadius:["none","","full",hn,H],borderSpacing:A(),borderWidth:B(),contrast:U(),grayscale:M(),hueRotate:U(),invert:M(),gap:A(),gradientColorStops:[e],gradientColorStopPositions:[rE,fn],inset:F(),margin:F(),opacity:U(),padding:A(),saturate:U(),scale:U(),sepia:M(),skew:U(),space:A(),translate:A()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[hn]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Y(),H]}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Ko,H]}],basis:[{basis:F()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:M()}],shrink:[{shrink:M()}],order:[{order:["first","last","none",Ko,H]}],"grid-cols":[{"grid-cols":[Go]}],"col-start-end":[{col:["auto",{span:["full",Ko,H]},H]}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":[Go]}],"row-start-end":[{row:["auto",{span:[Ko,H]},H]}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...T()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...T(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...T(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[w]}],mx:[{mx:[w]}],my:[{my:[w]}],ms:[{ms:[w]}],me:[{me:[w]}],mt:[{mt:[w]}],mr:[{mr:[w]}],mb:[{mb:[w]}],ml:[{ml:[w]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,t]}],"min-w":[{"min-w":[H,t,"min","max","fit"]}],"max-w":[{"max-w":[H,t,"none","full","min","max","fit","prose",{screen:[hn]},hn]}],h:[{h:[H,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,t,"auto","min","max","fit"]}],"font-size":[{text:["base",hn,fn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Hl]}],"font-family":[{font:[Go]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",Zr,Hl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",qt,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",qt,fn]}],"underline-offset":[{"underline-offset":["auto",qt,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Y(),iE]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",sE]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},lE]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[S]}],"gradient-via-pos":[{via:[S]}],"gradient-to-pos":[{to:[S]}],"gradient-from":[{from:[x]}],"gradient-via":[{via:[x]}],"gradient-to":[{to:[x]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...z(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...z()]}],"outline-offset":[{"outline-offset":[qt,H]}],"outline-w":[{outline:[qt,fn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[qt,fn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",hn,uE]}],"shadow-color":[{shadow:[Go]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",hn,H]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[y]}],sepia:[{sepia:[b]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[b]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:U()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:U()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[Ko,H]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[qt,fn,Hl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},pE=GC(hE);function X(...e){return pE(bv(e))}const mE=EC,Dv=p.forwardRef(({className:e,...t},n)=>u.jsx(vv,{ref:n,className:X("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Dv.displayName=vv.displayName;const gE=Js("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Iv=p.forwardRef(({className:e,variant:t,...n},r)=>u.jsx(yv,{ref:r,className:X(gE({variant:t}),e),...n}));Iv.displayName=yv.displayName;const vE=p.forwardRef(({className:e,...t},n)=>u.jsx(Sv,{ref:n,className:X("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));vE.displayName=Sv.displayName;const Lv=p.forwardRef(({className:e,...t},n)=>u.jsx(Cv,{ref:n,className:X("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:u.jsx(Rv,{className:"h-4 w-4"})}));Lv.displayName=Cv.displayName;const Fv=p.forwardRef(({className:e,...t},n)=>u.jsx(xv,{ref:n,className:X("text-sm font-semibold",e),...t}));Fv.displayName=xv.displayName;const zv=p.forwardRef(({className:e,...t},n)=>u.jsx(wv,{ref:n,className:X("text-sm opacity-90",e),...t}));zv.displayName=wv.displayName;function yE(){const{toasts:e}=Xa();return u.jsxs(mE,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return u.jsxs(Iv,{...s,children:[u.jsxs("div",{className:"grid gap-1",children:[n&&u.jsx(Fv,{children:n}),r&&u.jsx(zv,{children:r})]}),o,u.jsx(Lv,{})]},t)}),u.jsx(Dv,{})]})}var xE=bp[" useId ".trim().toString()]||(()=>{}),wE=0;function Jr(e){const[t,n]=p.useState(xE());return We(()=>{n(r=>r??String(wE++))},[e]),t?`radix-${t}`:""}const SE=["top","right","bottom","left"],Wn=Math.min,st=Math.max,Na=Math.round,Pi=Math.floor,Yt=e=>({x:e,y:e}),CE={left:"right",right:"left",bottom:"top",top:"bottom"},EE={start:"end",end:"start"};function hc(e,t,n){return st(e,Wn(t,n))}function ln(e,t){return typeof e=="function"?e(t):e}function un(e){return e.split("-")[0]}function Ao(e){return e.split("-")[1]}function Od(e){return e==="x"?"y":"x"}function Md(e){return e==="y"?"height":"width"}function Un(e){return["top","bottom"].includes(un(e))?"y":"x"}function _d(e){return Od(Un(e))}function bE(e,t,n){n===void 0&&(n=!1);const r=Ao(e),o=_d(e),s=Md(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Pa(i)),[i,Pa(i)]}function NE(e){const t=Pa(e);return[pc(e),t,pc(t)]}function pc(e){return e.replace(/start|end/g,t=>EE[t])}function PE(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function kE(e,t,n,r){const o=Ao(e);let s=PE(un(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(pc)))),s}function Pa(e){return e.replace(/left|right|bottom|top/g,t=>CE[t])}function TE(e){return{top:0,right:0,bottom:0,left:0,...e}}function $v(e){return typeof e!="number"?TE(e):{top:e,right:e,bottom:e,left:e}}function ka(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Rh(e,t,n){let{reference:r,floating:o}=e;const s=Un(t),i=_d(t),a=Md(i),l=un(t),c=s==="y",d=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,h=r[a]/2-o[a]/2;let x;switch(l){case"top":x={x:d,y:r.y-o.height};break;case"bottom":x={x:d,y:r.y+r.height};break;case"right":x={x:r.x+r.width,y:f};break;case"left":x={x:r.x-o.width,y:f};break;default:x={x:r.x,y:r.y}}switch(Ao(t)){case"start":x[i]-=h*(n&&c?-1:1);break;case"end":x[i]+=h*(n&&c?-1:1);break}return x}const RE=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:f}=Rh(c,r,l),h=r,x={},S=0;for(let m=0;m<a.length;m++){const{name:w,fn:v}=a[m],{x:g,y,data:C,reset:b}=await v({x:d,y:f,initialPlacement:r,placement:h,strategy:o,middlewareData:x,rects:c,platform:i,elements:{reference:e,floating:t}});d=g??d,f=y??f,x={...x,[w]:{...x[w],...C}},b&&S<=50&&(S++,typeof b=="object"&&(b.placement&&(h=b.placement),b.rects&&(c=b.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:d,y:f}=Rh(c,h,l)),m=-1)}return{x:d,y:f,placement:h,strategy:o,middlewareData:x}};async function Ds(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:h=!1,padding:x=0}=ln(t,e),S=$v(x),w=a[h?f==="floating"?"reference":"floating":f],v=ka(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(w)))==null||n?w:w.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:l})),g=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),C=await(s.isElement==null?void 0:s.isElement(y))?await(s.getScale==null?void 0:s.getScale(y))||{x:1,y:1}:{x:1,y:1},b=ka(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:l}):g);return{top:(v.top-b.top+S.top)/C.y,bottom:(b.bottom-v.bottom+S.bottom)/C.y,left:(v.left-b.left+S.left)/C.x,right:(b.right-v.right+S.right)/C.x}}const jE=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:c,padding:d=0}=ln(e,t)||{};if(c==null)return{};const f=$v(d),h={x:n,y:r},x=_d(o),S=Md(x),m=await i.getDimensions(c),w=x==="y",v=w?"top":"left",g=w?"bottom":"right",y=w?"clientHeight":"clientWidth",C=s.reference[S]+s.reference[x]-h[x]-s.floating[S],b=h[x]-s.reference[x],P=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let N=P?P[y]:0;(!N||!await(i.isElement==null?void 0:i.isElement(P)))&&(N=a.floating[y]||s.floating[S]);const k=C/2-b/2,j=N/2-m[S]/2-1,O=Wn(f[v],j),F=Wn(f[g],j),A=O,B=N-m[S]-F,_=N/2-m[S]/2+k,Y=hc(A,_,B),z=!l.arrow&&Ao(o)!=null&&_!==Y&&s.reference[S]/2-(_<A?O:F)-m[S]/2<0,V=z?_<A?_-A:_-B:0;return{[x]:h[x]+V,data:{[x]:Y,centerOffset:_-Y-V,...z&&{alignmentOffset:V}},reset:z}}}),OE=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:m=!0,...w}=ln(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const v=un(o),g=Un(a),y=un(a)===a,C=await(l.isRTL==null?void 0:l.isRTL(c.floating)),b=h||(y||!m?[Pa(a)]:NE(a)),P=S!=="none";!h&&P&&b.push(...kE(a,m,S,C));const N=[a,...b],k=await Ds(t,w),j=[];let O=((r=s.flip)==null?void 0:r.overflows)||[];if(d&&j.push(k[v]),f){const _=bE(o,i,C);j.push(k[_[0]],k[_[1]])}if(O=[...O,{placement:o,overflows:j}],!j.every(_=>_<=0)){var F,A;const _=(((F=s.flip)==null?void 0:F.index)||0)+1,Y=N[_];if(Y)return{data:{index:_,overflows:O},reset:{placement:Y}};let z=(A=O.filter(V=>V.overflows[0]<=0).sort((V,T)=>V.overflows[1]-T.overflows[1])[0])==null?void 0:A.placement;if(!z)switch(x){case"bestFit":{var B;const V=(B=O.filter(T=>{if(P){const M=Un(T.placement);return M===g||M==="y"}return!0}).map(T=>[T.placement,T.overflows.filter(M=>M>0).reduce((M,L)=>M+L,0)]).sort((T,M)=>T[1]-M[1])[0])==null?void 0:B[0];V&&(z=V);break}case"initialPlacement":z=a;break}if(o!==z)return{reset:{placement:z}}}return{}}}};function jh(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Oh(e){return SE.some(t=>e[t]>=0)}const ME=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ln(e,t);switch(r){case"referenceHidden":{const s=await Ds(t,{...o,elementContext:"reference"}),i=jh(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Oh(i)}}}case"escaped":{const s=await Ds(t,{...o,altBoundary:!0}),i=jh(s,n.floating);return{data:{escapedOffsets:i,escaped:Oh(i)}}}default:return{}}}}};async function _E(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=un(n),a=Ao(n),l=Un(n)==="y",c=["left","top"].includes(i)?-1:1,d=s&&l?-1:1,f=ln(t,e);let{mainAxis:h,crossAxis:x,alignmentAxis:S}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof S=="number"&&(x=a==="end"?S*-1:S),l?{x:x*d,y:h*c}:{x:h*c,y:x*d}}const AE=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await _E(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},DE=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:w=>{let{x:v,y:g}=w;return{x:v,y:g}}},...l}=ln(e,t),c={x:n,y:r},d=await Ds(t,l),f=Un(un(o)),h=Od(f);let x=c[h],S=c[f];if(s){const w=h==="y"?"top":"left",v=h==="y"?"bottom":"right",g=x+d[w],y=x-d[v];x=hc(g,x,y)}if(i){const w=f==="y"?"top":"left",v=f==="y"?"bottom":"right",g=S+d[w],y=S-d[v];S=hc(g,S,y)}const m=a.fn({...t,[h]:x,[f]:S});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[h]:s,[f]:i}}}}}},IE=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=ln(e,t),d={x:n,y:r},f=Un(o),h=Od(f);let x=d[h],S=d[f];const m=ln(a,t),w=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){const y=h==="y"?"height":"width",C=s.reference[h]-s.floating[y]+w.mainAxis,b=s.reference[h]+s.reference[y]-w.mainAxis;x<C?x=C:x>b&&(x=b)}if(c){var v,g;const y=h==="y"?"width":"height",C=["top","left"].includes(un(o)),b=s.reference[f]-s.floating[y]+(C&&((v=i.offset)==null?void 0:v[f])||0)+(C?0:w.crossAxis),P=s.reference[f]+s.reference[y]+(C?0:((g=i.offset)==null?void 0:g[f])||0)-(C?w.crossAxis:0);S<b?S=b:S>P&&(S=P)}return{[h]:x,[f]:S}}}},LE=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...c}=ln(e,t),d=await Ds(t,c),f=un(o),h=Ao(o),x=Un(o)==="y",{width:S,height:m}=s.floating;let w,v;f==="top"||f==="bottom"?(w=f,v=h===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(v=f,w=h==="end"?"top":"bottom");const g=m-d.top-d.bottom,y=S-d.left-d.right,C=Wn(m-d[w],g),b=Wn(S-d[v],y),P=!t.middlewareData.shift;let N=C,k=b;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(k=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=g),P&&!h){const O=st(d.left,0),F=st(d.right,0),A=st(d.top,0),B=st(d.bottom,0);x?k=S-2*(O!==0||F!==0?O+F:st(d.left,d.right)):N=m-2*(A!==0||B!==0?A+B:st(d.top,d.bottom))}await l({...t,availableWidth:k,availableHeight:N});const j=await i.getDimensions(a.floating);return S!==j.width||m!==j.height?{reset:{rects:!0}}:{}}}};function tl(){return typeof window<"u"}function Do(e){return Wv(e)?(e.nodeName||"").toLowerCase():"#document"}function lt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Gt(e){var t;return(t=(Wv(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Wv(e){return tl()?e instanceof Node||e instanceof lt(e).Node:!1}function Mt(e){return tl()?e instanceof Element||e instanceof lt(e).Element:!1}function Kt(e){return tl()?e instanceof HTMLElement||e instanceof lt(e).HTMLElement:!1}function Mh(e){return!tl()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof lt(e).ShadowRoot}function ei(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=_t(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function FE(e){return["table","td","th"].includes(Do(e))}function nl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Ad(e){const t=Dd(),n=Mt(e)?_t(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function zE(e){let t=Bn(e);for(;Kt(t)&&!No(t);){if(Ad(t))return t;if(nl(t))return null;t=Bn(t)}return null}function Dd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function No(e){return["html","body","#document"].includes(Do(e))}function _t(e){return lt(e).getComputedStyle(e)}function rl(e){return Mt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Bn(e){if(Do(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Mh(e)&&e.host||Gt(e);return Mh(t)?t.host:t}function Uv(e){const t=Bn(e);return No(t)?e.ownerDocument?e.ownerDocument.body:e.body:Kt(t)&&ei(t)?t:Uv(t)}function Is(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Uv(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=lt(o);if(s){const a=mc(i);return t.concat(i,i.visualViewport||[],ei(o)?o:[],a&&n?Is(a):[])}return t.concat(o,Is(o,[],n))}function mc(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Bv(e){const t=_t(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Kt(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=Na(n)!==s||Na(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function Id(e){return Mt(e)?e:e.contextElement}function eo(e){const t=Id(e);if(!Kt(t))return Yt(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Bv(t);let i=(s?Na(n.width):n.width)/r,a=(s?Na(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const $E=Yt(0);function Vv(e){const t=lt(e);return!Dd()||!t.visualViewport?$E:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function WE(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==lt(e)?!1:t}function wr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=Id(e);let i=Yt(1);t&&(r?Mt(r)&&(i=eo(r)):i=eo(e));const a=WE(s,n,r)?Vv(s):Yt(0);let l=(o.left+a.x)/i.x,c=(o.top+a.y)/i.y,d=o.width/i.x,f=o.height/i.y;if(s){const h=lt(s),x=r&&Mt(r)?lt(r):r;let S=h,m=mc(S);for(;m&&r&&x!==S;){const w=eo(m),v=m.getBoundingClientRect(),g=_t(m),y=v.left+(m.clientLeft+parseFloat(g.paddingLeft))*w.x,C=v.top+(m.clientTop+parseFloat(g.paddingTop))*w.y;l*=w.x,c*=w.y,d*=w.x,f*=w.y,l+=y,c+=C,S=lt(m),m=mc(S)}}return ka({width:d,height:f,x:l,y:c})}function Ld(e,t){const n=rl(e).scrollLeft;return t?t.left+n:wr(Gt(e)).left+n}function Hv(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Ld(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function UE(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Gt(r),a=t?nl(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},c=Yt(1);const d=Yt(0),f=Kt(r);if((f||!f&&!s)&&((Do(r)!=="body"||ei(i))&&(l=rl(r)),Kt(r))){const x=wr(r);c=eo(r),d.x=x.x+r.clientLeft,d.y=x.y+r.clientTop}const h=i&&!f&&!s?Hv(i,l,!0):Yt(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+h.x,y:n.y*c.y-l.scrollTop*c.y+d.y+h.y}}function BE(e){return Array.from(e.getClientRects())}function VE(e){const t=Gt(e),n=rl(e),r=e.ownerDocument.body,o=st(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=st(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Ld(e);const a=-n.scrollTop;return _t(r).direction==="rtl"&&(i+=st(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function HE(e,t){const n=lt(e),r=Gt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const c=Dd();(!c||c&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}function QE(e,t){const n=wr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Kt(e)?eo(e):Yt(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,c=r*s.y;return{width:i,height:a,x:l,y:c}}function _h(e,t,n){let r;if(t==="viewport")r=HE(e,n);else if(t==="document")r=VE(Gt(e));else if(Mt(t))r=QE(t,n);else{const o=Vv(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return ka(r)}function Qv(e,t){const n=Bn(e);return n===t||!Mt(n)||No(n)?!1:_t(n).position==="fixed"||Qv(n,t)}function YE(e,t){const n=t.get(e);if(n)return n;let r=Is(e,[],!1).filter(a=>Mt(a)&&Do(a)!=="body"),o=null;const s=_t(e).position==="fixed";let i=s?Bn(e):e;for(;Mt(i)&&!No(i);){const a=_t(i),l=Ad(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ei(i)&&!l&&Qv(e,i))?r=r.filter(d=>d!==i):o=a,i=Bn(i)}return t.set(e,r),r}function KE(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?nl(t)?[]:YE(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((c,d)=>{const f=_h(t,d,o);return c.top=st(f.top,c.top),c.right=Wn(f.right,c.right),c.bottom=Wn(f.bottom,c.bottom),c.left=st(f.left,c.left),c},_h(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function GE(e){const{width:t,height:n}=Bv(e);return{width:t,height:n}}function qE(e,t,n){const r=Kt(t),o=Gt(t),s=n==="fixed",i=wr(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=Yt(0);if(r||!r&&!s)if((Do(t)!=="body"||ei(o))&&(a=rl(t)),r){const h=wr(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else o&&(l.x=Ld(o));const c=o&&!r&&!s?Hv(o,a):Yt(0),d=i.left+a.scrollLeft-l.x-c.x,f=i.top+a.scrollTop-l.y-c.y;return{x:d,y:f,width:i.width,height:i.height}}function Ql(e){return _t(e).position==="static"}function Ah(e,t){if(!Kt(e)||_t(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Gt(e)===n&&(n=n.ownerDocument.body),n}function Yv(e,t){const n=lt(e);if(nl(e))return n;if(!Kt(e)){let o=Bn(e);for(;o&&!No(o);){if(Mt(o)&&!Ql(o))return o;o=Bn(o)}return n}let r=Ah(e,t);for(;r&&FE(r)&&Ql(r);)r=Ah(r,t);return r&&No(r)&&Ql(r)&&!Ad(r)?n:r||zE(e)||n}const XE=async function(e){const t=this.getOffsetParent||Yv,n=this.getDimensions,r=await n(e.floating);return{reference:qE(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function ZE(e){return _t(e).direction==="rtl"}const JE={convertOffsetParentRelativeRectToViewportRelativeRect:UE,getDocumentElement:Gt,getClippingRect:KE,getOffsetParent:Yv,getElementRects:XE,getClientRects:BE,getDimensions:GE,getScale:eo,isElement:Mt,isRTL:ZE};function Kv(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function eb(e,t){let n=null,r;const o=Gt(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const c=e.getBoundingClientRect(),{left:d,top:f,width:h,height:x}=c;if(a||t(),!h||!x)return;const S=Pi(f),m=Pi(o.clientWidth-(d+h)),w=Pi(o.clientHeight-(f+x)),v=Pi(d),y={rootMargin:-S+"px "+-m+"px "+-w+"px "+-v+"px",threshold:st(0,Wn(1,l))||1};let C=!0;function b(P){const N=P[0].intersectionRatio;if(N!==l){if(!C)return i();N?i(!1,N):r=setTimeout(()=>{i(!1,1e-7)},1e3)}N===1&&!Kv(c,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(b,y)}n.observe(e)}return i(!0),s}function tb(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,c=Id(e),d=o||s?[...c?Is(c):[],...Is(t)]:[];d.forEach(v=>{o&&v.addEventListener("scroll",n,{passive:!0}),s&&v.addEventListener("resize",n)});const f=c&&a?eb(c,n):null;let h=-1,x=null;i&&(x=new ResizeObserver(v=>{let[g]=v;g&&g.target===c&&x&&(x.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var y;(y=x)==null||y.observe(t)})),n()}),c&&!l&&x.observe(c),x.observe(t));let S,m=l?wr(e):null;l&&w();function w(){const v=wr(e);m&&!Kv(m,v)&&n(),m=v,S=requestAnimationFrame(w)}return n(),()=>{var v;d.forEach(g=>{o&&g.removeEventListener("scroll",n),s&&g.removeEventListener("resize",n)}),f==null||f(),(v=x)==null||v.disconnect(),x=null,l&&cancelAnimationFrame(S)}}const nb=AE,rb=DE,ob=OE,sb=LE,ib=ME,Dh=jE,ab=IE,lb=(e,t,n)=>{const r=new Map,o={platform:JE,...n},s={...o.platform,_c:r};return RE(e,t,{...o,platform:s})};var Yi=typeof document<"u"?p.useLayoutEffect:p.useEffect;function Ta(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ta(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Ta(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Gv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ih(e,t){const n=Gv(e);return Math.round(t*n)/n}function Yl(e){const t=p.useRef(e);return Yi(()=>{t.current=e}),t}function ub(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:c}=e,[d,f]=p.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,x]=p.useState(r);Ta(h,r)||x(r);const[S,m]=p.useState(null),[w,v]=p.useState(null),g=p.useCallback(T=>{T!==P.current&&(P.current=T,m(T))},[]),y=p.useCallback(T=>{T!==N.current&&(N.current=T,v(T))},[]),C=s||S,b=i||w,P=p.useRef(null),N=p.useRef(null),k=p.useRef(d),j=l!=null,O=Yl(l),F=Yl(o),A=Yl(c),B=p.useCallback(()=>{if(!P.current||!N.current)return;const T={placement:t,strategy:n,middleware:h};F.current&&(T.platform=F.current),lb(P.current,N.current,T).then(M=>{const L={...M,isPositioned:A.current!==!1};_.current&&!Ta(k.current,L)&&(k.current=L,Nr.flushSync(()=>{f(L)}))})},[h,t,n,F,A]);Yi(()=>{c===!1&&k.current.isPositioned&&(k.current.isPositioned=!1,f(T=>({...T,isPositioned:!1})))},[c]);const _=p.useRef(!1);Yi(()=>(_.current=!0,()=>{_.current=!1}),[]),Yi(()=>{if(C&&(P.current=C),b&&(N.current=b),C&&b){if(O.current)return O.current(C,b,B);B()}},[C,b,B,O,j]);const Y=p.useMemo(()=>({reference:P,floating:N,setReference:g,setFloating:y}),[g,y]),z=p.useMemo(()=>({reference:C,floating:b}),[C,b]),V=p.useMemo(()=>{const T={position:n,left:0,top:0};if(!z.floating)return T;const M=Ih(z.floating,d.x),L=Ih(z.floating,d.y);return a?{...T,transform:"translate("+M+"px, "+L+"px)",...Gv(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:M,top:L}},[n,a,z.floating,d.x,d.y]);return p.useMemo(()=>({...d,update:B,refs:Y,elements:z,floatingStyles:V}),[d,B,Y,z,V])}const cb=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Dh({element:r.current,padding:o}).fn(n):{}:r?Dh({element:r,padding:o}).fn(n):{}}}},db=(e,t)=>({...nb(e),options:[e,t]}),fb=(e,t)=>({...rb(e),options:[e,t]}),hb=(e,t)=>({...ab(e),options:[e,t]}),pb=(e,t)=>({...ob(e),options:[e,t]}),mb=(e,t)=>({...sb(e),options:[e,t]}),gb=(e,t)=>({...ib(e),options:[e,t]}),vb=(e,t)=>({...cb(e),options:[e,t]});var yb="Arrow",qv=p.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return u.jsx(J.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:u.jsx("polygon",{points:"0,0 30,0 15,10"})})});qv.displayName=yb;var xb=qv;function wb(e){const[t,n]=p.useState(void 0);return We(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,c=Array.isArray(l)?l[0]:l;i=c.inlineSize,a=c.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Fd="Popper",[Xv,ol]=Oo(Fd),[Sb,Zv]=Xv(Fd),Jv=e=>{const{__scopePopper:t,children:n}=e,[r,o]=p.useState(null);return u.jsx(Sb,{scope:t,anchor:r,onAnchorChange:o,children:n})};Jv.displayName=Fd;var ey="PopperAnchor",ty=p.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Zv(ey,n),i=p.useRef(null),a=he(t,i);return p.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:u.jsx(J.div,{...o,ref:a})});ty.displayName=ey;var zd="PopperContent",[Cb,Eb]=Xv(zd),ny=p.forwardRef((e,t)=>{var $,ue,Ae,ae,oe,se;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:c=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:h=!1,updatePositionStrategy:x="optimized",onPlaced:S,...m}=e,w=Zv(zd,n),[v,g]=p.useState(null),y=he(t,nt=>g(nt)),[C,b]=p.useState(null),P=wb(C),N=(P==null?void 0:P.width)??0,k=(P==null?void 0:P.height)??0,j=r+(s!=="center"?"-"+s:""),O=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},F=Array.isArray(c)?c:[c],A=F.length>0,B={padding:O,boundary:F.filter(Nb),altBoundary:A},{refs:_,floatingStyles:Y,placement:z,isPositioned:V,middlewareData:T}=ub({strategy:"fixed",placement:j,whileElementsMounted:(...nt)=>tb(...nt,{animationFrame:x==="always"}),elements:{reference:w.anchor},middleware:[db({mainAxis:o+k,alignmentAxis:i}),l&&fb({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?hb():void 0,...B}),l&&pb({...B}),mb({...B,apply:({elements:nt,rects:It,availableWidth:Lo,availableHeight:Fo})=>{const{width:zo,height:Q0}=It.reference,ri=nt.floating.style;ri.setProperty("--radix-popper-available-width",`${Lo}px`),ri.setProperty("--radix-popper-available-height",`${Fo}px`),ri.setProperty("--radix-popper-anchor-width",`${zo}px`),ri.setProperty("--radix-popper-anchor-height",`${Q0}px`)}}),C&&vb({element:C,padding:a}),Pb({arrowWidth:N,arrowHeight:k}),h&&gb({strategy:"referenceHidden",...B})]}),[M,L]=sy(z),U=ct(S);We(()=>{V&&(U==null||U())},[V,U]);const re=($=T.arrow)==null?void 0:$.x,Ue=(ue=T.arrow)==null?void 0:ue.y,Ee=((Ae=T.arrow)==null?void 0:Ae.centerOffset)!==0,[Dt,Be]=p.useState();return We(()=>{v&&Be(window.getComputedStyle(v).zIndex)},[v]),u.jsx("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...Y,transform:V?Y.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Dt,"--radix-popper-transform-origin":[(ae=T.transformOrigin)==null?void 0:ae.x,(oe=T.transformOrigin)==null?void 0:oe.y].join(" "),...((se=T.hide)==null?void 0:se.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:u.jsx(Cb,{scope:n,placedSide:M,onArrowChange:b,arrowX:re,arrowY:Ue,shouldHideArrow:Ee,children:u.jsx(J.div,{"data-side":M,"data-align":L,...m,ref:y,style:{...m.style,animation:V?void 0:"none"}})})})});ny.displayName=zd;var ry="PopperArrow",bb={top:"bottom",right:"left",bottom:"top",left:"right"},oy=p.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=Eb(ry,r),i=bb[s.placedSide];return u.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:u.jsx(xb,{...o,ref:n,style:{...o.style,display:"block"}})})});oy.displayName=ry;function Nb(e){return e!==null}var Pb=e=>({name:"transformOrigin",options:e,fn(t){var w,v,g;const{placement:n,rects:r,middlewareData:o}=t,i=((w=o.arrow)==null?void 0:w.centerOffset)!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,d]=sy(n),f={start:"0%",center:"50%",end:"100%"}[d],h=(((v=o.arrow)==null?void 0:v.x)??0)+a/2,x=(((g=o.arrow)==null?void 0:g.y)??0)+l/2;let S="",m="";return c==="bottom"?(S=i?f:`${h}px`,m=`${-l}px`):c==="top"?(S=i?f:`${h}px`,m=`${r.floating.height+l}px`):c==="right"?(S=`${-l}px`,m=i?f:`${x}px`):c==="left"&&(S=`${r.floating.width+l}px`,m=i?f:`${x}px`),{data:{x:S,y:m}}}});function sy(e){const[t,n="center"]=e.split("-");return[t,n]}var kb=Jv,iy=ty,ay=ny,ly=oy,[sl,p2]=Oo("Tooltip",[ol]),$d=ol(),uy="TooltipProvider",Tb=700,Lh="tooltip.open",[Rb,cy]=sl(uy),dy=e=>{const{__scopeTooltip:t,delayDuration:n=Tb,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,i=p.useRef(!0),a=p.useRef(!1),l=p.useRef(0);return p.useEffect(()=>{const c=l.current;return()=>window.clearTimeout(c)},[]),u.jsx(Rb,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:p.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:p.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:p.useCallback(c=>{a.current=c},[]),disableHoverableContent:o,children:s})};dy.displayName=uy;var fy="Tooltip",[m2,il]=sl(fy),gc="TooltipTrigger",jb=p.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=il(gc,n),s=cy(gc,n),i=$d(n),a=p.useRef(null),l=he(t,a,o.onTriggerChange),c=p.useRef(!1),d=p.useRef(!1),f=p.useCallback(()=>c.current=!1,[]);return p.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),u.jsx(iy,{asChild:!0,...i,children:u.jsx(J.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:Q(e.onPointerMove,h=>{h.pointerType!=="touch"&&!d.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:Q(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:Q(e.onPointerDown,()=>{o.open&&o.onClose(),c.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:Q(e.onFocus,()=>{c.current||o.onOpen()}),onBlur:Q(e.onBlur,o.onClose),onClick:Q(e.onClick,o.onClose)})})});jb.displayName=gc;var Ob="TooltipPortal",[g2,Mb]=sl(Ob,{forceMount:void 0}),Po="TooltipContent",hy=p.forwardRef((e,t)=>{const n=Mb(Po,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=il(Po,e.__scopeTooltip);return u.jsx(Mo,{present:r||i.open,children:i.disableHoverableContent?u.jsx(py,{side:o,...s,ref:t}):u.jsx(_b,{side:o,...s,ref:t})})}),_b=p.forwardRef((e,t)=>{const n=il(Po,e.__scopeTooltip),r=cy(Po,e.__scopeTooltip),o=p.useRef(null),s=he(t,o),[i,a]=p.useState(null),{trigger:l,onClose:c}=n,d=o.current,{onPointerInTransitChange:f}=r,h=p.useCallback(()=>{a(null),f(!1)},[f]),x=p.useCallback((S,m)=>{const w=S.currentTarget,v={x:S.clientX,y:S.clientY},g=Fb(v,w.getBoundingClientRect()),y=zb(v,g),C=$b(m.getBoundingClientRect()),b=Ub([...y,...C]);a(b),f(!0)},[f]);return p.useEffect(()=>()=>h(),[h]),p.useEffect(()=>{if(l&&d){const S=w=>x(w,d),m=w=>x(w,l);return l.addEventListener("pointerleave",S),d.addEventListener("pointerleave",m),()=>{l.removeEventListener("pointerleave",S),d.removeEventListener("pointerleave",m)}}},[l,d,x,h]),p.useEffect(()=>{if(i){const S=m=>{const w=m.target,v={x:m.clientX,y:m.clientY},g=(l==null?void 0:l.contains(w))||(d==null?void 0:d.contains(w)),y=!Wb(v,i);g?h():y&&(h(),c())};return document.addEventListener("pointermove",S),()=>document.removeEventListener("pointermove",S)}},[l,d,i,c,h]),u.jsx(py,{...e,ref:s})}),[Ab,Db]=sl(fy,{isInside:!1}),Ib=zS("TooltipContent"),py=p.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,l=il(Po,n),c=$d(n),{onClose:d}=l;return p.useEffect(()=>(document.addEventListener(Lh,d),()=>document.removeEventListener(Lh,d)),[d]),p.useEffect(()=>{if(l.trigger){const f=h=>{const x=h.target;x!=null&&x.contains(l.trigger)&&d()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,d]),u.jsx(Xs,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:d,children:u.jsxs(ay,{"data-state":l.stateAttribute,...c,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[u.jsx(Ib,{children:r}),u.jsx(Ab,{scope:n,isInside:!0,children:u.jsx(sC,{id:l.contentId,role:"tooltip",children:o||r})})]})})});hy.displayName=Po;var my="TooltipArrow",Lb=p.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=$d(n);return Db(my,n).isInside?null:u.jsx(ly,{...o,...r,ref:t})});Lb.displayName=my;function Fb(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function zb(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function $b(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Wb(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,c=t[i].x,d=t[i].y;l>r!=d>r&&n<(c-a)*(r-l)/(d-l)+a&&(o=!o)}return o}function Ub(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Bb(t)}function Bb(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Vb=dy,gy=hy;const Hb=Vb,Qb=p.forwardRef(({className:e,sideOffset:t=4,...n},r)=>u.jsx(gy,{ref:r,sideOffset:t,className:X("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",e),...n}));Qb.displayName=gy.displayName;function vy(){return jo({queryKey:["/api/business"],refetchInterval:3e4})}function Yb(){return jo({queryKey:["/api/setup"],refetchInterval:1e4})}const Kb=Js("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ze=p.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?LS:"button";return u.jsx(i,{className:X(Kb({variant:t,size:n,className:e})),ref:s,...o})});ze.displayName="Button";const Gb=Js("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function dr({className:e,variant:t,...n}){return u.jsx("div",{className:X(Gb({variant:t}),e),...n})}function ie(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}function Vn(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}const yy=6048e5,qb=864e5,ki=43200,Fh=1440;let Xb={};function ti(){return Xb}function Ls(e,t){var a,l,c,d;const n=ti(),r=(t==null?void 0:t.weekStartsOn)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.weekStartsOn)??n.weekStartsOn??((d=(c=n.locale)==null?void 0:c.options)==null?void 0:d.weekStartsOn)??0,o=ie(e),s=o.getDay(),i=(s<r?7:0)+s-r;return o.setDate(o.getDate()-i),o.setHours(0,0,0,0),o}function Ra(e){return Ls(e,{weekStartsOn:1})}function xy(e){const t=ie(e),n=t.getFullYear(),r=Vn(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const o=Ra(r),s=Vn(e,0);s.setFullYear(n,0,4),s.setHours(0,0,0,0);const i=Ra(s);return t.getTime()>=o.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function zh(e){const t=ie(e);return t.setHours(0,0,0,0),t}function ja(e){const t=ie(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function Zb(e,t){const n=zh(e),r=zh(t),o=+n-ja(n),s=+r-ja(r);return Math.round((o-s)/qb)}function Jb(e){const t=xy(e),n=Vn(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),Ra(n)}function Ki(e,t){const n=ie(e),r=ie(t),o=n.getTime()-r.getTime();return o<0?-1:o>0?1:o}function eN(e){return Vn(e,Date.now())}function tN(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function nN(e){if(!tN(e)&&typeof e!="number")return!1;const t=ie(e);return!isNaN(Number(t))}function rN(e,t){const n=ie(e),r=ie(t),o=n.getFullYear()-r.getFullYear(),s=n.getMonth()-r.getMonth();return o*12+s}function oN(e){return t=>{const r=(e?Math[e]:Math.trunc)(t);return r===0?0:r}}function sN(e,t){return+ie(e)-+ie(t)}function iN(e){const t=ie(e);return t.setHours(23,59,59,999),t}function aN(e){const t=ie(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}function lN(e){const t=ie(e);return+iN(t)==+aN(t)}function uN(e,t){const n=ie(e),r=ie(t),o=Ki(n,r),s=Math.abs(rN(n,r));let i;if(s<1)i=0;else{n.getMonth()===1&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-o*s);let a=Ki(n,r)===-o;lN(ie(e))&&s===1&&Ki(e,r)===1&&(a=!1),i=o*(s-Number(a))}return i===0?0:i}function cN(e,t,n){const r=sN(e,t)/1e3;return oN(n==null?void 0:n.roundingMethod)(r)}function dN(e){const t=ie(e),n=Vn(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}const fN={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},hN=(e,t,n)=>{let r;const o=fN[e];return typeof o=="string"?r=o:t===1?r=o.one:r=o.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function Kl(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const pN={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},mN={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},gN={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},vN={date:Kl({formats:pN,defaultWidth:"full"}),time:Kl({formats:mN,defaultWidth:"full"}),dateTime:Kl({formats:gN,defaultWidth:"full"})},yN={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},xN=(e,t,n,r)=>yN[e];function qo(e){return(t,n)=>{const r=n!=null&&n.context?String(n.context):"standalone";let o;if(r==="formatting"&&e.formattingValues){const i=e.defaultFormattingWidth||e.defaultWidth,a=n!=null&&n.width?String(n.width):i;o=e.formattingValues[a]||e.formattingValues[i]}else{const i=e.defaultWidth,a=n!=null&&n.width?String(n.width):e.defaultWidth;o=e.values[a]||e.values[i]}const s=e.argumentCallback?e.argumentCallback(t):t;return o[s]}}const wN={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},SN={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},CN={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},EN={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},bN={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},NN={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},PN=(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},kN={ordinalNumber:PN,era:qo({values:wN,defaultWidth:"wide"}),quarter:qo({values:SN,defaultWidth:"wide",argumentCallback:e=>e-1}),month:qo({values:CN,defaultWidth:"wide"}),day:qo({values:EN,defaultWidth:"wide"}),dayPeriod:qo({values:bN,defaultWidth:"wide",formattingValues:NN,defaultFormattingWidth:"wide"})};function Xo(e){return(t,n={})=>{const r=n.width,o=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],s=t.match(o);if(!s)return null;const i=s[0],a=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(a)?RN(a,f=>f.test(i)):TN(a,f=>f.test(i));let c;c=e.valueCallback?e.valueCallback(l):l,c=n.valueCallback?n.valueCallback(c):c;const d=t.slice(i.length);return{value:c,rest:d}}}function TN(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}function RN(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}function jN(e){return(t,n={})=>{const r=t.match(e.matchPattern);if(!r)return null;const o=r[0],s=t.match(e.parsePattern);if(!s)return null;let i=e.valueCallback?e.valueCallback(s[0]):s[0];i=n.valueCallback?n.valueCallback(i):i;const a=t.slice(o.length);return{value:i,rest:a}}}const ON=/^(\d+)(th|st|nd|rd)?/i,MN=/\d+/i,_N={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},AN={any:[/^b/i,/^(a|c)/i]},DN={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},IN={any:[/1/i,/2/i,/3/i,/4/i]},LN={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},FN={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},zN={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},$N={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},WN={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},UN={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},BN={ordinalNumber:jN({matchPattern:ON,parsePattern:MN,valueCallback:e=>parseInt(e,10)}),era:Xo({matchPatterns:_N,defaultMatchWidth:"wide",parsePatterns:AN,defaultParseWidth:"any"}),quarter:Xo({matchPatterns:DN,defaultMatchWidth:"wide",parsePatterns:IN,defaultParseWidth:"any",valueCallback:e=>e+1}),month:Xo({matchPatterns:LN,defaultMatchWidth:"wide",parsePatterns:FN,defaultParseWidth:"any"}),day:Xo({matchPatterns:zN,defaultMatchWidth:"wide",parsePatterns:$N,defaultParseWidth:"any"}),dayPeriod:Xo({matchPatterns:WN,defaultMatchWidth:"any",parsePatterns:UN,defaultParseWidth:"any"})},wy={code:"en-US",formatDistance:hN,formatLong:vN,formatRelative:xN,localize:kN,match:BN,options:{weekStartsOn:0,firstWeekContainsDate:1}};function VN(e){const t=ie(e);return Zb(t,dN(t))+1}function HN(e){const t=ie(e),n=+Ra(t)-+Jb(t);return Math.round(n/yy)+1}function Sy(e,t){var d,f,h,x;const n=ie(e),r=n.getFullYear(),o=ti(),s=(t==null?void 0:t.firstWeekContainsDate)??((f=(d=t==null?void 0:t.locale)==null?void 0:d.options)==null?void 0:f.firstWeekContainsDate)??o.firstWeekContainsDate??((x=(h=o.locale)==null?void 0:h.options)==null?void 0:x.firstWeekContainsDate)??1,i=Vn(e,0);i.setFullYear(r+1,0,s),i.setHours(0,0,0,0);const a=Ls(i,t),l=Vn(e,0);l.setFullYear(r,0,s),l.setHours(0,0,0,0);const c=Ls(l,t);return n.getTime()>=a.getTime()?r+1:n.getTime()>=c.getTime()?r:r-1}function QN(e,t){var a,l,c,d;const n=ti(),r=(t==null?void 0:t.firstWeekContainsDate)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.firstWeekContainsDate)??n.firstWeekContainsDate??((d=(c=n.locale)==null?void 0:c.options)==null?void 0:d.firstWeekContainsDate)??1,o=Sy(e,t),s=Vn(e,0);return s.setFullYear(o,0,r),s.setHours(0,0,0,0),Ls(s,t)}function YN(e,t){const n=ie(e),r=+Ls(n,t)-+QN(n,t);return Math.round(r/yy)+1}function te(e,t){const n=e<0?"-":"",r=Math.abs(e).toString().padStart(t,"0");return n+r}const pn={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return te(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):te(n+1,2)},d(e,t){return te(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return te(e.getHours()%12||12,t.length)},H(e,t){return te(e.getHours(),t.length)},m(e,t){return te(e.getMinutes(),t.length)},s(e,t){return te(e.getSeconds(),t.length)},S(e,t){const n=t.length,r=e.getMilliseconds(),o=Math.trunc(r*Math.pow(10,n-3));return te(o,t.length)}},kr={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},$h={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const r=e.getFullYear(),o=r>0?r:1-r;return n.ordinalNumber(o,{unit:"year"})}return pn.y(e,t)},Y:function(e,t,n,r){const o=Sy(e,r),s=o>0?o:1-o;if(t==="YY"){const i=s%100;return te(i,2)}return t==="Yo"?n.ordinalNumber(s,{unit:"year"}):te(s,t.length)},R:function(e,t){const n=xy(e);return te(n,t.length)},u:function(e,t){const n=e.getFullYear();return te(n,t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return te(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return te(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return pn.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return te(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const o=YN(e,r);return t==="wo"?n.ordinalNumber(o,{unit:"week"}):te(o,t.length)},I:function(e,t,n){const r=HN(e);return t==="Io"?n.ordinalNumber(r,{unit:"week"}):te(r,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):pn.d(e,t)},D:function(e,t,n){const r=VN(e);return t==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):te(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const o=e.getDay(),s=(o-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return te(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});case"eeee":default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const o=e.getDay(),s=(o-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return te(s,t.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});case"cccc":default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),o=r===0?7:r;switch(t){case"i":return String(o);case"ii":return te(o,t.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const o=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let o;switch(r===12?o=kr.noon:r===0?o=kr.midnight:o=r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let o;switch(r>=17?o=kr.evening:r>=12?o=kr.afternoon:r>=4?o=kr.morning:o=kr.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let r=e.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return pn.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):pn.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return t==="Ko"?n.ordinalNumber(r,{unit:"hour"}):te(r,t.length)},k:function(e,t,n){let r=e.getHours();return r===0&&(r=24),t==="ko"?n.ordinalNumber(r,{unit:"hour"}):te(r,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):pn.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):pn.s(e,t)},S:function(e,t){return pn.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return Uh(r);case"XXXX":case"XX":return Zn(r);case"XXXXX":case"XXX":default:return Zn(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return Uh(r);case"xxxx":case"xx":return Zn(r);case"xxxxx":case"xxx":default:return Zn(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Wh(r,":");case"OOOO":default:return"GMT"+Zn(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Wh(r,":");case"zzzz":default:return"GMT"+Zn(r,":")}},t:function(e,t,n){const r=Math.trunc(e.getTime()/1e3);return te(r,t.length)},T:function(e,t,n){const r=e.getTime();return te(r,t.length)}};function Wh(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),o=Math.trunc(r/60),s=r%60;return s===0?n+String(o):n+String(o)+t+te(s,2)}function Uh(e,t){return e%60===0?(e>0?"-":"+")+te(Math.abs(e)/60,2):Zn(e,t)}function Zn(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),o=te(Math.trunc(r/60),2),s=te(r%60,2);return n+o+t+s}const Bh=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},Cy=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},KN=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],o=n[2];if(!o)return Bh(e,t);let s;switch(r){case"P":s=t.dateTime({width:"short"});break;case"PP":s=t.dateTime({width:"medium"});break;case"PPP":s=t.dateTime({width:"long"});break;case"PPPP":default:s=t.dateTime({width:"full"});break}return s.replace("{{date}}",Bh(r,t)).replace("{{time}}",Cy(o,t))},GN={p:Cy,P:KN},qN=/^D+$/,XN=/^Y+$/,ZN=["D","DD","YY","YYYY"];function JN(e){return qN.test(e)}function eP(e){return XN.test(e)}function tP(e,t,n){const r=nP(e,t,n);if(console.warn(r),ZN.includes(e))throw new RangeError(r)}function nP(e,t,n){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const rP=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,oP=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,sP=/^'([^]*?)'?$/,iP=/''/g,aP=/[a-zA-Z]/;function Gl(e,t,n){var d,f,h,x;const r=ti(),o=r.locale??wy,s=r.firstWeekContainsDate??((f=(d=r.locale)==null?void 0:d.options)==null?void 0:f.firstWeekContainsDate)??1,i=r.weekStartsOn??((x=(h=r.locale)==null?void 0:h.options)==null?void 0:x.weekStartsOn)??0,a=ie(e);if(!nN(a))throw new RangeError("Invalid time value");let l=t.match(oP).map(S=>{const m=S[0];if(m==="p"||m==="P"){const w=GN[m];return w(S,o.formatLong)}return S}).join("").match(rP).map(S=>{if(S==="''")return{isToken:!1,value:"'"};const m=S[0];if(m==="'")return{isToken:!1,value:lP(S)};if($h[m])return{isToken:!0,value:S};if(m.match(aP))throw new RangeError("Format string contains an unescaped latin alphabet character `"+m+"`");return{isToken:!1,value:S}});o.localize.preprocessor&&(l=o.localize.preprocessor(a,l));const c={firstWeekContainsDate:s,weekStartsOn:i,locale:o};return l.map(S=>{if(!S.isToken)return S.value;const m=S.value;(eP(m)||JN(m))&&tP(m,t,String(e));const w=$h[m[0]];return w(a,m,o.localize,c)}).join("")}function lP(e){const t=e.match(sP);return t?t[1].replace(iP,"'"):e}function uP(e,t,n){const r=ti(),o=(n==null?void 0:n.locale)??r.locale??wy,s=2520,i=Ki(e,t);if(isNaN(i))throw new RangeError("Invalid time value");const a=Object.assign({},n,{addSuffix:n==null?void 0:n.addSuffix,comparison:i});let l,c;i>0?(l=ie(t),c=ie(e)):(l=ie(e),c=ie(t));const d=cN(c,l),f=(ja(c)-ja(l))/1e3,h=Math.round((d-f)/60);let x;if(h<2)return n!=null&&n.includeSeconds?d<5?o.formatDistance("lessThanXSeconds",5,a):d<10?o.formatDistance("lessThanXSeconds",10,a):d<20?o.formatDistance("lessThanXSeconds",20,a):d<40?o.formatDistance("halfAMinute",0,a):d<60?o.formatDistance("lessThanXMinutes",1,a):o.formatDistance("xMinutes",1,a):h===0?o.formatDistance("lessThanXMinutes",1,a):o.formatDistance("xMinutes",h,a);if(h<45)return o.formatDistance("xMinutes",h,a);if(h<90)return o.formatDistance("aboutXHours",1,a);if(h<Fh){const S=Math.round(h/60);return o.formatDistance("aboutXHours",S,a)}else{if(h<s)return o.formatDistance("xDays",1,a);if(h<ki){const S=Math.round(h/Fh);return o.formatDistance("xDays",S,a)}else if(h<ki*2)return x=Math.round(h/ki),o.formatDistance("aboutXMonths",x,a)}if(x=uN(c,l),x<12){const S=Math.round(h/ki);return o.formatDistance("xMonths",S,a)}else{const S=x%12,m=Math.trunc(x/12);return S<3?o.formatDistance("aboutXYears",m,a):S<9?o.formatDistance("overXYears",m,a):o.formatDistance("almostXYears",m+1,a)}}function cP(e,t){return uP(e,eN(e),t)}function dP(){var d,f;const{data:e}=vy(),{toast:t}=Xa(),[n,r]=p.useState(!1),[o,s]=p.useState(!1),[,i]=Cd(),a=async()=>{r(!0);try{const x=await(await Xr("POST","/api/sync")).json();x.success?t({title:"Sync Completed",description:`Found ${x.newReviews} new reviews`}):x.quotaExceeded?t({title:"Google API Quota Exceeded",description:x.message||"API quota limits reached. Your 163 reviews will be fetched automatically when quota resets.",variant:"default"}):t({title:"Sync Failed",description:x.message||x.error||"Failed to sync reviews",variant:"destructive"})}catch{t({title:"Sync Error",description:"Failed to sync reviews. Please try again later.",variant:"destructive"})}finally{r(!1)}},l=async()=>{s(!0);try{const x=await(await Xr("POST","/api/logout")).json();x.success?(Ca.invalidateQueries(),t({title:"Logged out successfully",description:"You have been disconnected from your Google account."}),setTimeout(()=>{i("/setup"),setTimeout(()=>{window.location.reload()},100)},1e3)):t({title:"Logout failed",description:x.error||"Unable to logout. Please try again.",variant:"destructive"})}catch{t({title:"Logout error",description:"Failed to logout. Please try again later.",variant:"destructive"})}finally{s(!1)}},c=(d=e==null?void 0:e.businessInfo)!=null&&d.lastSync?`${cP(new Date(e.businessInfo.lastSync))} ago`:"Never";return u.jsx("nav",{className:"bg-white border-b border-slate-200 sticky top-0 z-50",children:u.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:u.jsxs("div",{className:"flex justify-between items-center h-16",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsx(ba,{className:"text-hotel-blue text-2xl"}),u.jsx("h1",{className:"text-xl font-semibold text-slate-900",children:((f=e==null?void 0:e.businessInfo)==null?void 0:f.name)||"Hotel Reviews"})]}),u.jsx(dr,{variant:"secondary",children:"Review Management"})]}),u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsxs(ze,{onClick:a,disabled:n,className:"bg-hotel-blue hover:bg-hotel-blue/90",children:[u.jsx(kv,{className:`h-4 w-4 mr-2 ${n?"animate-spin":""}`}),n?"Syncing...":"Sync Reviews"]}),u.jsxs(ze,{onClick:l,disabled:o,variant:"outline",className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:[u.jsx(MC,{className:`h-4 w-4 mr-2 ${o?"animate-pulse":""}`}),o?"Logging out...":"Logout"]}),u.jsxs("div",{className:"flex items-center space-x-2 text-sm text-slate-600",children:[u.jsx(_s,{className:"h-4 w-4"}),u.jsxs("span",{children:["Last sync: ",c]})]})]})]})})})}const Ne=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:X("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Ne.displayName="Card";const Fs=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:X("flex flex-col space-y-1.5 p-6",e),...t}));Fs.displayName="CardHeader";const zs=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:X("text-2xl font-semibold leading-none tracking-tight",e),...t}));zs.displayName="CardTitle";const Ey=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:X("text-sm text-muted-foreground",e),...t}));Ey.displayName="CardDescription";const Pe=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:X("p-6 pt-0",e),...t}));Pe.displayName="CardContent";const fP=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:X("flex items-center p-6 pt-0",e),...t}));fP.displayName="CardFooter";function hP(){const{data:e,isLoading:t}=jo({queryKey:["/api/reviews/stats"]});if(t)return u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[...Array(4)].map((r,o)=>u.jsx(Ne,{className:"animate-pulse",children:u.jsx(Pe,{className:"p-6",children:u.jsx("div",{className:"h-16 bg-gray-200 rounded"})})},o))});const n=r=>Array.from({length:5},(o,s)=>u.jsx(As,{className:`h-4 w-4 ${s<Math.floor(r)?"text-hotel-yellow fill-current":s<r?"text-hotel-yellow fill-current opacity-50":"text-gray-300"}`},s));return u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[u.jsx(Ne,{className:"gradient-primary text-white",children:u.jsxs(Pe,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-white/80 text-sm font-medium",children:"Total Reviews"}),u.jsx("p",{className:"text-3xl font-bold",children:(e==null?void 0:e.totalReviews)||0})]}),u.jsx("div",{className:"bg-white/20 rounded-lg p-3",children:u.jsx(As,{className:"h-6 w-6"})})]}),u.jsx("div",{className:"mt-4",children:u.jsxs("span",{className:"text-green-200 text-sm font-medium",children:["+",(e==null?void 0:e.thisMonth)||0," this month"]})})]})}),u.jsx(Ne,{children:u.jsxs(Pe,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-600 text-sm font-medium",children:"Average Rating"}),u.jsx("p",{className:"text-3xl font-bold text-gray-900",children:(e==null?void 0:e.averageRating)||0})]}),u.jsx("div",{className:"bg-yellow-50 rounded-lg p-3",children:u.jsx(IC,{className:"h-6 w-6 text-hotel-yellow"})})]}),u.jsx("div",{className:"mt-4 flex items-center",children:u.jsx("div",{className:"flex",children:n((e==null?void 0:e.averageRating)||0)})})]})}),u.jsx(Ne,{children:u.jsxs(Pe,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-600 text-sm font-medium",children:"Response Rate"}),u.jsxs("p",{className:"text-3xl font-bold text-gray-900",children:[(e==null?void 0:e.responseRate)||0,"%"]})]}),u.jsx("div",{className:"bg-green-50 rounded-lg p-3",children:u.jsx(_C,{className:"h-6 w-6 text-hotel-green"})})]}),u.jsx("div",{className:"mt-4",children:u.jsx("span",{className:"text-hotel-green text-sm font-medium",children:"Great response rate!"})})]})}),u.jsx(Ne,{children:u.jsxs(Pe,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-600 text-sm font-medium",children:"This Month"}),u.jsx("p",{className:"text-3xl font-bold text-gray-900",children:(e==null?void 0:e.thisMonth)||0})]}),u.jsx("div",{className:"bg-blue-50 rounded-lg p-3",children:u.jsx(kC,{className:"h-6 w-6 text-hotel-blue"})})]}),u.jsx("div",{className:"mt-4",children:u.jsx("span",{className:"text-hotel-blue text-sm font-medium",children:"New reviews received"})})]})})]})}function pP(){const{data:e,isLoading:t}=jo({queryKey:["/api/reviews/stats"]});if(t)return u.jsxs(Ne,{className:"mb-8",children:[u.jsx(Fs,{children:u.jsx(zs,{children:"Rating Distribution"})}),u.jsx(Pe,{children:u.jsx("div",{className:"space-y-3",children:[...Array(5)].map((s,i)=>u.jsxs("div",{className:"flex items-center animate-pulse",children:[u.jsx("div",{className:"w-8 h-4 bg-gray-200 rounded mr-4"}),u.jsx("div",{className:"flex-1 h-2 bg-gray-200 rounded mr-4"}),u.jsx("div",{className:"w-12 h-4 bg-gray-200 rounded"})]},i))})})]});const n=(e==null?void 0:e.ratingCounts)||[0,0,0,0,0],r=n.reduce((s,i)=>s+i,0),o=s=>s>=4?"bg-hotel-green":s===3?"bg-hotel-yellow":"bg-hotel-red";return u.jsxs(Ne,{className:"mb-8",children:[u.jsx(Fs,{children:u.jsx(zs,{className:"text-lg font-semibold text-slate-900",children:"Rating Distribution"})}),u.jsx(Pe,{children:u.jsx("div",{className:"space-y-3",children:[5,4,3,2,1].map((s,i)=>{const a=n[s-1],l=r>0?a/r*100:0;return u.jsxs("div",{className:"flex items-center",children:[u.jsxs("div",{className:"flex items-center space-x-2 w-20",children:[u.jsx("span",{className:"text-sm font-medium text-slate-700",children:s}),u.jsx(As,{className:"h-4 w-4 text-hotel-yellow fill-current"})]}),u.jsx("div",{className:"flex-1 mx-4",children:u.jsx("div",{className:"bg-slate-200 rounded-full h-2",children:u.jsx("div",{className:`h-2 rounded-full ${o(s)}`,style:{width:`${l}%`}})})}),u.jsx("span",{className:"text-sm text-slate-600 w-12 text-right",children:a})]},s)})})})]})}function Vh(e,[t,n]){return Math.min(n,Math.max(t,e))}var mP=p.createContext(void 0);function gP(e){const t=p.useContext(mP);return e||t||"ltr"}var ql=0;function by(){p.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Hh()),document.body.insertAdjacentElement("beforeend",e[1]??Hh()),ql++,()=>{ql===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ql--}},[])}function Hh(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Xl="focusScope.autoFocusOnMount",Zl="focusScope.autoFocusOnUnmount",Qh={bubbles:!1,cancelable:!0},vP="FocusScope",Wd=p.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[a,l]=p.useState(null),c=ct(o),d=ct(s),f=p.useRef(null),h=he(t,m=>l(m)),x=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(r){let m=function(y){if(x.paused||!a)return;const C=y.target;a.contains(C)?f.current=C:vn(f.current,{select:!0})},w=function(y){if(x.paused||!a)return;const C=y.relatedTarget;C!==null&&(a.contains(C)||vn(f.current,{select:!0}))},v=function(y){if(document.activeElement===document.body)for(const b of y)b.removedNodes.length>0&&vn(a)};document.addEventListener("focusin",m),document.addEventListener("focusout",w);const g=new MutationObserver(v);return a&&g.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",w),g.disconnect()}}},[r,a,x.paused]),p.useEffect(()=>{if(a){Kh.add(x);const m=document.activeElement;if(!a.contains(m)){const v=new CustomEvent(Xl,Qh);a.addEventListener(Xl,c),a.dispatchEvent(v),v.defaultPrevented||(yP(EP(Ny(a)),{select:!0}),document.activeElement===m&&vn(a))}return()=>{a.removeEventListener(Xl,c),setTimeout(()=>{const v=new CustomEvent(Zl,Qh);a.addEventListener(Zl,d),a.dispatchEvent(v),v.defaultPrevented||vn(m??document.body,{select:!0}),a.removeEventListener(Zl,d),Kh.remove(x)},0)}}},[a,c,d,x]);const S=p.useCallback(m=>{if(!n&&!r||x.paused)return;const w=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,v=document.activeElement;if(w&&v){const g=m.currentTarget,[y,C]=xP(g);y&&C?!m.shiftKey&&v===C?(m.preventDefault(),n&&vn(y,{select:!0})):m.shiftKey&&v===y&&(m.preventDefault(),n&&vn(C,{select:!0})):v===g&&m.preventDefault()}},[n,r,x.paused]);return u.jsx(J.div,{tabIndex:-1,...i,ref:h,onKeyDown:S})});Wd.displayName=vP;function yP(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(vn(r,{select:t}),document.activeElement!==n)return}function xP(e){const t=Ny(e),n=Yh(t,e),r=Yh(t.reverse(),e);return[n,r]}function Ny(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Yh(e,t){for(const n of e)if(!wP(n,{upTo:t}))return n}function wP(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function SP(e){return e instanceof HTMLInputElement&&"select"in e}function vn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&SP(e)&&t&&e.select()}}var Kh=CP();function CP(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Gh(e,t),e.unshift(t)},remove(t){var n;e=Gh(e,t),(n=e[0])==null||n.resume()}}}function Gh(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function EP(e){return e.filter(t=>t.tagName!=="A")}function bP(e){const t=p.useRef({value:e,previous:e});return p.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var NP=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Tr=new WeakMap,Ti=new WeakMap,Ri={},Jl=0,Py=function(e){return e&&(e.host||Py(e.parentNode))},PP=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Py(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},kP=function(e,t,n,r){var o=PP(t,Array.isArray(e)?e:[e]);Ri[n]||(Ri[n]=new WeakMap);var s=Ri[n],i=[],a=new Set,l=new Set(o),c=function(f){!f||a.has(f)||(a.add(f),c(f.parentNode))};o.forEach(c);var d=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(h){if(a.has(h))d(h);else try{var x=h.getAttribute(r),S=x!==null&&x!=="false",m=(Tr.get(h)||0)+1,w=(s.get(h)||0)+1;Tr.set(h,m),s.set(h,w),i.push(h),m===1&&S&&Ti.set(h,!0),w===1&&h.setAttribute(n,"true"),S||h.setAttribute(r,"true")}catch(v){console.error("aria-hidden: cannot operate on ",h,v)}})};return d(t),a.clear(),Jl++,function(){i.forEach(function(f){var h=Tr.get(f)-1,x=s.get(f)-1;Tr.set(f,h),s.set(f,x),h||(Ti.has(f)||f.removeAttribute(r),Ti.delete(f)),x||f.removeAttribute(n)}),Jl--,Jl||(Tr=new WeakMap,Tr=new WeakMap,Ti=new WeakMap,Ri={})}},ky=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=NP(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),kP(r,o,n,"aria-hidden")):function(){return null}},Vt=function(){return Vt=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Vt.apply(this,arguments)};function Ty(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function TP(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var Gi="right-scroll-bar-position",qi="width-before-scroll-bar",RP="with-scroll-bars-hidden",jP="--removed-body-scroll-bar-size";function eu(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function OP(e,t){var n=p.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var MP=typeof window<"u"?p.useLayoutEffect:p.useEffect,qh=new WeakMap;function _P(e,t){var n=OP(null,function(r){return e.forEach(function(o){return eu(o,r)})});return MP(function(){var r=qh.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(a){s.has(a)||eu(a,null)}),s.forEach(function(a){o.has(a)||eu(a,i)})}qh.set(n,e)},[e]),n}function AP(e){return e}function DP(e,t){t===void 0&&(t=AP);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var l=function(){var d=i;i=[],d.forEach(s)},c=function(){return Promise.resolve().then(l)};c(),n={push:function(d){i.push(d),c()},filter:function(d){return i=i.filter(d),n}}}};return o}function IP(e){e===void 0&&(e={});var t=DP(null);return t.options=Vt({async:!0,ssr:!1},e),t}var Ry=function(e){var t=e.sideCar,n=Ty(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return p.createElement(r,Vt({},n))};Ry.isSideCarExport=!0;function LP(e,t){return e.useMedium(t),Ry}var jy=IP(),tu=function(){},al=p.forwardRef(function(e,t){var n=p.useRef(null),r=p.useState({onScrollCapture:tu,onWheelCapture:tu,onTouchMoveCapture:tu}),o=r[0],s=r[1],i=e.forwardProps,a=e.children,l=e.className,c=e.removeScrollBar,d=e.enabled,f=e.shards,h=e.sideCar,x=e.noIsolation,S=e.inert,m=e.allowPinchZoom,w=e.as,v=w===void 0?"div":w,g=e.gapMode,y=Ty(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=h,b=_P([n,t]),P=Vt(Vt({},y),o);return p.createElement(p.Fragment,null,d&&p.createElement(C,{sideCar:jy,removeScrollBar:c,shards:f,noIsolation:x,inert:S,setCallbacks:s,allowPinchZoom:!!m,lockRef:n,gapMode:g}),i?p.cloneElement(p.Children.only(a),Vt(Vt({},P),{ref:b})):p.createElement(v,Vt({},P,{className:l,ref:b}),a))});al.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};al.classNames={fullWidth:qi,zeroRight:Gi};var FP=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function zP(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=FP();return t&&e.setAttribute("nonce",t),e}function $P(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function WP(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var UP=function(){var e=0,t=null;return{add:function(n){e==0&&(t=zP())&&($P(t,n),WP(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},BP=function(){var e=UP();return function(t,n){p.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Oy=function(){var e=BP(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},VP={left:0,top:0,right:0,gap:0},nu=function(e){return parseInt(e||"",10)||0},HP=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[nu(n),nu(r),nu(o)]},QP=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return VP;var t=HP(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},YP=Oy(),to="data-scroll-locked",KP=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(RP,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(to,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Gi,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(qi,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Gi," .").concat(Gi,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(qi," .").concat(qi,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(to,`] {
    `).concat(jP,": ").concat(a,`px;
  }
`)},Xh=function(){var e=parseInt(document.body.getAttribute(to)||"0",10);return isFinite(e)?e:0},GP=function(){p.useEffect(function(){return document.body.setAttribute(to,(Xh()+1).toString()),function(){var e=Xh()-1;e<=0?document.body.removeAttribute(to):document.body.setAttribute(to,e.toString())}},[])},qP=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;GP();var s=p.useMemo(function(){return QP(o)},[o]);return p.createElement(YP,{styles:KP(s,!t,o,n?"":"!important")})},vc=!1;if(typeof window<"u")try{var ji=Object.defineProperty({},"passive",{get:function(){return vc=!0,!0}});window.addEventListener("test",ji,ji),window.removeEventListener("test",ji,ji)}catch{vc=!1}var Rr=vc?{passive:!1}:!1,XP=function(e){return e.tagName==="TEXTAREA"},My=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!XP(e)&&n[t]==="visible")},ZP=function(e){return My(e,"overflowY")},JP=function(e){return My(e,"overflowX")},Zh=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=_y(e,r);if(o){var s=Ay(e,r),i=s[1],a=s[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ek=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},tk=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},_y=function(e,t){return e==="v"?ZP(t):JP(t)},Ay=function(e,t){return e==="v"?ek(t):tk(t)},nk=function(e,t){return e==="h"&&t==="rtl"?-1:1},rk=function(e,t,n,r,o){var s=nk(e,window.getComputedStyle(t).direction),i=s*r,a=n.target,l=t.contains(a),c=!1,d=i>0,f=0,h=0;do{var x=Ay(e,a),S=x[0],m=x[1],w=x[2],v=m-w-s*S;(S||v)&&_y(e,a)&&(f+=v,h+=S),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(d&&(Math.abs(f)<1||!o)||!d&&(Math.abs(h)<1||!o))&&(c=!0),c},Oi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Jh=function(e){return[e.deltaX,e.deltaY]},ep=function(e){return e&&"current"in e?e.current:e},ok=function(e,t){return e[0]===t[0]&&e[1]===t[1]},sk=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ik=0,jr=[];function ak(e){var t=p.useRef([]),n=p.useRef([0,0]),r=p.useRef(),o=p.useState(ik++)[0],s=p.useState(Oy)[0],i=p.useRef(e);p.useEffect(function(){i.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=TP([e.lockRef.current],(e.shards||[]).map(ep),!0).filter(Boolean);return m.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=p.useCallback(function(m,w){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!i.current.allowPinchZoom;var v=Oi(m),g=n.current,y="deltaX"in m?m.deltaX:g[0]-v[0],C="deltaY"in m?m.deltaY:g[1]-v[1],b,P=m.target,N=Math.abs(y)>Math.abs(C)?"h":"v";if("touches"in m&&N==="h"&&P.type==="range")return!1;var k=Zh(N,P);if(!k)return!0;if(k?b=N:(b=N==="v"?"h":"v",k=Zh(N,P)),!k)return!1;if(!r.current&&"changedTouches"in m&&(y||C)&&(r.current=b),!b)return!0;var j=r.current||b;return rk(j,w,m,j==="h"?y:C,!0)},[]),l=p.useCallback(function(m){var w=m;if(!(!jr.length||jr[jr.length-1]!==s)){var v="deltaY"in w?Jh(w):Oi(w),g=t.current.filter(function(b){return b.name===w.type&&(b.target===w.target||w.target===b.shadowParent)&&ok(b.delta,v)})[0];if(g&&g.should){w.cancelable&&w.preventDefault();return}if(!g){var y=(i.current.shards||[]).map(ep).filter(Boolean).filter(function(b){return b.contains(w.target)}),C=y.length>0?a(w,y[0]):!i.current.noIsolation;C&&w.cancelable&&w.preventDefault()}}},[]),c=p.useCallback(function(m,w,v,g){var y={name:m,delta:w,target:v,should:g,shadowParent:lk(v)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(C){return C!==y})},1)},[]),d=p.useCallback(function(m){n.current=Oi(m),r.current=void 0},[]),f=p.useCallback(function(m){c(m.type,Jh(m),m.target,a(m,e.lockRef.current))},[]),h=p.useCallback(function(m){c(m.type,Oi(m),m.target,a(m,e.lockRef.current))},[]);p.useEffect(function(){return jr.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:h}),document.addEventListener("wheel",l,Rr),document.addEventListener("touchmove",l,Rr),document.addEventListener("touchstart",d,Rr),function(){jr=jr.filter(function(m){return m!==s}),document.removeEventListener("wheel",l,Rr),document.removeEventListener("touchmove",l,Rr),document.removeEventListener("touchstart",d,Rr)}},[]);var x=e.removeScrollBar,S=e.inert;return p.createElement(p.Fragment,null,S?p.createElement(s,{styles:sk(o)}):null,x?p.createElement(qP,{gapMode:e.gapMode}):null)}function lk(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const uk=LP(jy,ak);var Ud=p.forwardRef(function(e,t){return p.createElement(al,Vt({},e,{ref:t,sideCar:uk}))});Ud.classNames=al.classNames;var ck=[" ","Enter","ArrowUp","ArrowDown"],dk=[" ","Enter"],ni="Select",[ll,ul,fk]=Jg(ni),[Io,v2]=Oo(ni,[fk,ol]),cl=ol(),[hk,Kn]=Io(ni),[pk,mk]=Io(ni),Dy=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:s,value:i,defaultValue:a,onValueChange:l,dir:c,name:d,autoComplete:f,disabled:h,required:x,form:S}=e,m=cl(t),[w,v]=p.useState(null),[g,y]=p.useState(null),[C,b]=p.useState(!1),P=gP(c),[N=!1,k]=Ea({prop:r,defaultProp:o,onChange:s}),[j,O]=Ea({prop:i,defaultProp:a,onChange:l}),F=p.useRef(null),A=w?S||!!w.closest("form"):!0,[B,_]=p.useState(new Set),Y=Array.from(B).map(z=>z.props.value).join(";");return u.jsx(kb,{...m,children:u.jsxs(hk,{required:x,scope:t,trigger:w,onTriggerChange:v,valueNode:g,onValueNodeChange:y,valueNodeHasChildren:C,onValueNodeHasChildrenChange:b,contentId:Jr(),value:j,onValueChange:O,open:N,onOpenChange:k,dir:P,triggerPointerDownPosRef:F,disabled:h,children:[u.jsx(ll.Provider,{scope:t,children:u.jsx(pk,{scope:e.__scopeSelect,onNativeOptionAdd:p.useCallback(z=>{_(V=>new Set(V).add(z))},[]),onNativeOptionRemove:p.useCallback(z=>{_(V=>{const T=new Set(V);return T.delete(z),T})},[]),children:n})}),A?u.jsxs(i0,{"aria-hidden":!0,required:x,tabIndex:-1,name:d,autoComplete:f,value:j,onChange:z=>O(z.target.value),disabled:h,form:S,children:[j===void 0?u.jsx("option",{value:""}):null,Array.from(B)]},Y):null]})})};Dy.displayName=ni;var Iy="SelectTrigger",Ly=p.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,s=cl(n),i=Kn(Iy,n),a=i.disabled||r,l=he(t,i.onTriggerChange),c=ul(n),d=p.useRef("touch"),[f,h,x]=a0(m=>{const w=c().filter(y=>!y.disabled),v=w.find(y=>y.value===i.value),g=l0(w,m,v);g!==void 0&&i.onValueChange(g.value)}),S=m=>{a||(i.onOpenChange(!0),x()),m&&(i.triggerPointerDownPosRef.current={x:Math.round(m.pageX),y:Math.round(m.pageY)})};return u.jsx(iy,{asChild:!0,...s,children:u.jsx(J.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":s0(i.value)?"":void 0,...o,ref:l,onClick:Q(o.onClick,m=>{m.currentTarget.focus(),d.current!=="mouse"&&S(m)}),onPointerDown:Q(o.onPointerDown,m=>{d.current=m.pointerType;const w=m.target;w.hasPointerCapture(m.pointerId)&&w.releasePointerCapture(m.pointerId),m.button===0&&m.ctrlKey===!1&&m.pointerType==="mouse"&&(S(m),m.preventDefault())}),onKeyDown:Q(o.onKeyDown,m=>{const w=f.current!=="";!(m.ctrlKey||m.altKey||m.metaKey)&&m.key.length===1&&h(m.key),!(w&&m.key===" ")&&ck.includes(m.key)&&(S(),m.preventDefault())})})})});Ly.displayName=Iy;var Fy="SelectValue",zy=p.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:s,placeholder:i="",...a}=e,l=Kn(Fy,n),{onValueNodeHasChildrenChange:c}=l,d=s!==void 0,f=he(t,l.onValueNodeChange);return We(()=>{c(d)},[c,d]),u.jsx(J.span,{...a,ref:f,style:{pointerEvents:"none"},children:s0(l.value)?u.jsx(u.Fragment,{children:i}):s})});zy.displayName=Fy;var gk="SelectIcon",$y=p.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return u.jsx(J.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});$y.displayName=gk;var vk="SelectPortal",Wy=e=>u.jsx(Za,{asChild:!0,...e});Wy.displayName=vk;var Sr="SelectContent",Uy=p.forwardRef((e,t)=>{const n=Kn(Sr,e.__scopeSelect),[r,o]=p.useState();if(We(()=>{o(new DocumentFragment)},[]),!n.open){const s=r;return s?Nr.createPortal(u.jsx(By,{scope:e.__scopeSelect,children:u.jsx(ll.Slot,{scope:e.__scopeSelect,children:u.jsx("div",{children:e.children})})}),s):null}return u.jsx(Vy,{...e,ref:t})});Uy.displayName=Sr;var Et=10,[By,Gn]=Io(Sr),yk="SelectContentImpl",xk=bo("SelectContent.RemoveScroll"),Vy=p.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:s,onPointerDownOutside:i,side:a,sideOffset:l,align:c,alignOffset:d,arrowPadding:f,collisionBoundary:h,collisionPadding:x,sticky:S,hideWhenDetached:m,avoidCollisions:w,...v}=e,g=Kn(Sr,n),[y,C]=p.useState(null),[b,P]=p.useState(null),N=he(t,$=>C($)),[k,j]=p.useState(null),[O,F]=p.useState(null),A=ul(n),[B,_]=p.useState(!1),Y=p.useRef(!1);p.useEffect(()=>{if(y)return ky(y)},[y]),by();const z=p.useCallback($=>{const[ue,...Ae]=A().map(se=>se.ref.current),[ae]=Ae.slice(-1),oe=document.activeElement;for(const se of $)if(se===oe||(se==null||se.scrollIntoView({block:"nearest"}),se===ue&&b&&(b.scrollTop=0),se===ae&&b&&(b.scrollTop=b.scrollHeight),se==null||se.focus(),document.activeElement!==oe))return},[A,b]),V=p.useCallback(()=>z([k,y]),[z,k,y]);p.useEffect(()=>{B&&V()},[B,V]);const{onOpenChange:T,triggerPointerDownPosRef:M}=g;p.useEffect(()=>{if(y){let $={x:0,y:0};const ue=ae=>{var oe,se;$={x:Math.abs(Math.round(ae.pageX)-(((oe=M.current)==null?void 0:oe.x)??0)),y:Math.abs(Math.round(ae.pageY)-(((se=M.current)==null?void 0:se.y)??0))}},Ae=ae=>{$.x<=10&&$.y<=10?ae.preventDefault():y.contains(ae.target)||T(!1),document.removeEventListener("pointermove",ue),M.current=null};return M.current!==null&&(document.addEventListener("pointermove",ue),document.addEventListener("pointerup",Ae,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ue),document.removeEventListener("pointerup",Ae,{capture:!0})}}},[y,T,M]),p.useEffect(()=>{const $=()=>T(!1);return window.addEventListener("blur",$),window.addEventListener("resize",$),()=>{window.removeEventListener("blur",$),window.removeEventListener("resize",$)}},[T]);const[L,U]=a0($=>{const ue=A().filter(oe=>!oe.disabled),Ae=ue.find(oe=>oe.ref.current===document.activeElement),ae=l0(ue,$,Ae);ae&&setTimeout(()=>ae.ref.current.focus())}),re=p.useCallback(($,ue,Ae)=>{const ae=!Y.current&&!Ae;(g.value!==void 0&&g.value===ue||ae)&&(j($),ae&&(Y.current=!0))},[g.value]),Ue=p.useCallback(()=>y==null?void 0:y.focus(),[y]),Ee=p.useCallback(($,ue,Ae)=>{const ae=!Y.current&&!Ae;(g.value!==void 0&&g.value===ue||ae)&&F($)},[g.value]),Dt=r==="popper"?yc:Hy,Be=Dt===yc?{side:a,sideOffset:l,align:c,alignOffset:d,arrowPadding:f,collisionBoundary:h,collisionPadding:x,sticky:S,hideWhenDetached:m,avoidCollisions:w}:{};return u.jsx(By,{scope:n,content:y,viewport:b,onViewportChange:P,itemRefCallback:re,selectedItem:k,onItemLeave:Ue,itemTextRefCallback:Ee,focusSelectedItem:V,selectedItemText:O,position:r,isPositioned:B,searchRef:L,children:u.jsx(Ud,{as:xk,allowPinchZoom:!0,children:u.jsx(Wd,{asChild:!0,trapped:g.open,onMountAutoFocus:$=>{$.preventDefault()},onUnmountAutoFocus:Q(o,$=>{var ue;(ue=g.trigger)==null||ue.focus({preventScroll:!0}),$.preventDefault()}),children:u.jsx(Xs,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:$=>$.preventDefault(),onDismiss:()=>g.onOpenChange(!1),children:u.jsx(Dt,{role:"listbox",id:g.contentId,"data-state":g.open?"open":"closed",dir:g.dir,onContextMenu:$=>$.preventDefault(),...v,...Be,onPlaced:()=>_(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:Q(v.onKeyDown,$=>{const ue=$.ctrlKey||$.altKey||$.metaKey;if($.key==="Tab"&&$.preventDefault(),!ue&&$.key.length===1&&U($.key),["ArrowUp","ArrowDown","Home","End"].includes($.key)){let ae=A().filter(oe=>!oe.disabled).map(oe=>oe.ref.current);if(["ArrowUp","End"].includes($.key)&&(ae=ae.slice().reverse()),["ArrowUp","ArrowDown"].includes($.key)){const oe=$.target,se=ae.indexOf(oe);ae=ae.slice(se+1)}setTimeout(()=>z(ae)),$.preventDefault()}})})})})})})});Vy.displayName=yk;var wk="SelectItemAlignedPosition",Hy=p.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,s=Kn(Sr,n),i=Gn(Sr,n),[a,l]=p.useState(null),[c,d]=p.useState(null),f=he(t,N=>d(N)),h=ul(n),x=p.useRef(!1),S=p.useRef(!0),{viewport:m,selectedItem:w,selectedItemText:v,focusSelectedItem:g}=i,y=p.useCallback(()=>{if(s.trigger&&s.valueNode&&a&&c&&m&&w&&v){const N=s.trigger.getBoundingClientRect(),k=c.getBoundingClientRect(),j=s.valueNode.getBoundingClientRect(),O=v.getBoundingClientRect();if(s.dir!=="rtl"){const oe=O.left-k.left,se=j.left-oe,nt=N.left-se,It=N.width+nt,Lo=Math.max(It,k.width),Fo=window.innerWidth-Et,zo=Vh(se,[Et,Math.max(Et,Fo-Lo)]);a.style.minWidth=It+"px",a.style.left=zo+"px"}else{const oe=k.right-O.right,se=window.innerWidth-j.right-oe,nt=window.innerWidth-N.right-se,It=N.width+nt,Lo=Math.max(It,k.width),Fo=window.innerWidth-Et,zo=Vh(se,[Et,Math.max(Et,Fo-Lo)]);a.style.minWidth=It+"px",a.style.right=zo+"px"}const F=h(),A=window.innerHeight-Et*2,B=m.scrollHeight,_=window.getComputedStyle(c),Y=parseInt(_.borderTopWidth,10),z=parseInt(_.paddingTop,10),V=parseInt(_.borderBottomWidth,10),T=parseInt(_.paddingBottom,10),M=Y+z+B+T+V,L=Math.min(w.offsetHeight*5,M),U=window.getComputedStyle(m),re=parseInt(U.paddingTop,10),Ue=parseInt(U.paddingBottom,10),Ee=N.top+N.height/2-Et,Dt=A-Ee,Be=w.offsetHeight/2,$=w.offsetTop+Be,ue=Y+z+$,Ae=M-ue;if(ue<=Ee){const oe=F.length>0&&w===F[F.length-1].ref.current;a.style.bottom="0px";const se=c.clientHeight-m.offsetTop-m.offsetHeight,nt=Math.max(Dt,Be+(oe?Ue:0)+se+V),It=ue+nt;a.style.height=It+"px"}else{const oe=F.length>0&&w===F[0].ref.current;a.style.top="0px";const nt=Math.max(Ee,Y+m.offsetTop+(oe?re:0)+Be)+Ae;a.style.height=nt+"px",m.scrollTop=ue-Ee+m.offsetTop}a.style.margin=`${Et}px 0`,a.style.minHeight=L+"px",a.style.maxHeight=A+"px",r==null||r(),requestAnimationFrame(()=>x.current=!0)}},[h,s.trigger,s.valueNode,a,c,m,w,v,s.dir,r]);We(()=>y(),[y]);const[C,b]=p.useState();We(()=>{c&&b(window.getComputedStyle(c).zIndex)},[c]);const P=p.useCallback(N=>{N&&S.current===!0&&(y(),g==null||g(),S.current=!1)},[y,g]);return u.jsx(Ck,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:x,onScrollButtonChange:P,children:u.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:u.jsx(J.div,{...o,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});Hy.displayName=wk;var Sk="SelectPopperPosition",yc=p.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=Et,...s}=e,i=cl(n);return u.jsx(ay,{...i,...s,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});yc.displayName=Sk;var[Ck,Bd]=Io(Sr,{}),xc="SelectViewport",Qy=p.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,s=Gn(xc,n),i=Bd(xc,n),a=he(t,s.onViewportChange),l=p.useRef(0);return u.jsxs(u.Fragment,{children:[u.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),u.jsx(ll.Slot,{scope:n,children:u.jsx(J.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:Q(o.onScroll,c=>{const d=c.currentTarget,{contentWrapper:f,shouldExpandOnScrollRef:h}=i;if(h!=null&&h.current&&f){const x=Math.abs(l.current-d.scrollTop);if(x>0){const S=window.innerHeight-Et*2,m=parseFloat(f.style.minHeight),w=parseFloat(f.style.height),v=Math.max(m,w);if(v<S){const g=v+x,y=Math.min(S,g),C=g-y;f.style.height=y+"px",f.style.bottom==="0px"&&(d.scrollTop=C>0?C:0,f.style.justifyContent="flex-end")}}}l.current=d.scrollTop})})})]})});Qy.displayName=xc;var Yy="SelectGroup",[Ek,bk]=Io(Yy),Nk=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Jr();return u.jsx(Ek,{scope:n,id:o,children:u.jsx(J.div,{role:"group","aria-labelledby":o,...r,ref:t})})});Nk.displayName=Yy;var Ky="SelectLabel",Gy=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=bk(Ky,n);return u.jsx(J.div,{id:o.id,...r,ref:t})});Gy.displayName=Ky;var Oa="SelectItem",[Pk,qy]=Io(Oa),Xy=p.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:s,...i}=e,a=Kn(Oa,n),l=Gn(Oa,n),c=a.value===r,[d,f]=p.useState(s??""),[h,x]=p.useState(!1),S=he(t,g=>{var y;return(y=l.itemRefCallback)==null?void 0:y.call(l,g,r,o)}),m=Jr(),w=p.useRef("touch"),v=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return u.jsx(Pk,{scope:n,value:r,disabled:o,textId:m,isSelected:c,onItemTextChange:p.useCallback(g=>{f(y=>y||((g==null?void 0:g.textContent)??"").trim())},[]),children:u.jsx(ll.ItemSlot,{scope:n,value:r,disabled:o,textValue:d,children:u.jsx(J.div,{role:"option","aria-labelledby":m,"data-highlighted":h?"":void 0,"aria-selected":c&&h,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:S,onFocus:Q(i.onFocus,()=>x(!0)),onBlur:Q(i.onBlur,()=>x(!1)),onClick:Q(i.onClick,()=>{w.current!=="mouse"&&v()}),onPointerUp:Q(i.onPointerUp,()=>{w.current==="mouse"&&v()}),onPointerDown:Q(i.onPointerDown,g=>{w.current=g.pointerType}),onPointerMove:Q(i.onPointerMove,g=>{var y;w.current=g.pointerType,o?(y=l.onItemLeave)==null||y.call(l):w.current==="mouse"&&g.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Q(i.onPointerLeave,g=>{var y;g.currentTarget===document.activeElement&&((y=l.onItemLeave)==null||y.call(l))}),onKeyDown:Q(i.onKeyDown,g=>{var C;((C=l.searchRef)==null?void 0:C.current)!==""&&g.key===" "||(dk.includes(g.key)&&v(),g.key===" "&&g.preventDefault())})})})})});Xy.displayName=Oa;var rs="SelectItemText",Zy=p.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...s}=e,i=Kn(rs,n),a=Gn(rs,n),l=qy(rs,n),c=mk(rs,n),[d,f]=p.useState(null),h=he(t,v=>f(v),l.onItemTextChange,v=>{var g;return(g=a.itemTextRefCallback)==null?void 0:g.call(a,v,l.value,l.disabled)}),x=d==null?void 0:d.textContent,S=p.useMemo(()=>u.jsx("option",{value:l.value,disabled:l.disabled,children:x},l.value),[l.disabled,l.value,x]),{onNativeOptionAdd:m,onNativeOptionRemove:w}=c;return We(()=>(m(S),()=>w(S)),[m,w,S]),u.jsxs(u.Fragment,{children:[u.jsx(J.span,{id:l.textId,...s,ref:h}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?Nr.createPortal(s.children,i.valueNode):null]})});Zy.displayName=rs;var Jy="SelectItemIndicator",e0=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return qy(Jy,n).isSelected?u.jsx(J.span,{"aria-hidden":!0,...r,ref:t}):null});e0.displayName=Jy;var wc="SelectScrollUpButton",t0=p.forwardRef((e,t)=>{const n=Gn(wc,e.__scopeSelect),r=Bd(wc,e.__scopeSelect),[o,s]=p.useState(!1),i=he(t,r.onScrollButtonChange);return We(()=>{if(n.viewport&&n.isPositioned){let a=function(){const c=l.scrollTop>0;s(c)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),o?u.jsx(r0,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop-l.offsetHeight)}}):null});t0.displayName=wc;var Sc="SelectScrollDownButton",n0=p.forwardRef((e,t)=>{const n=Gn(Sc,e.__scopeSelect),r=Bd(Sc,e.__scopeSelect),[o,s]=p.useState(!1),i=he(t,r.onScrollButtonChange);return We(()=>{if(n.viewport&&n.isPositioned){let a=function(){const c=l.scrollHeight-l.clientHeight,d=Math.ceil(l.scrollTop)<c;s(d)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),o?u.jsx(r0,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop+l.offsetHeight)}}):null});n0.displayName=Sc;var r0=p.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,s=Gn("SelectScrollButton",n),i=p.useRef(null),a=ul(n),l=p.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return p.useEffect(()=>()=>l(),[l]),We(()=>{var d;const c=a().find(f=>f.ref.current===document.activeElement);(d=c==null?void 0:c.ref.current)==null||d.scrollIntoView({block:"nearest"})},[a]),u.jsx(J.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:Q(o.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(r,50))}),onPointerMove:Q(o.onPointerMove,()=>{var c;(c=s.onItemLeave)==null||c.call(s),i.current===null&&(i.current=window.setInterval(r,50))}),onPointerLeave:Q(o.onPointerLeave,()=>{l()})})}),kk="SelectSeparator",o0=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return u.jsx(J.div,{"aria-hidden":!0,...r,ref:t})});o0.displayName=kk;var Cc="SelectArrow",Tk=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=cl(n),s=Kn(Cc,n),i=Gn(Cc,n);return s.open&&i.position==="popper"?u.jsx(ly,{...o,...r,ref:t}):null});Tk.displayName=Cc;function s0(e){return e===""||e===void 0}var i0=p.forwardRef((e,t)=>{const{value:n,...r}=e,o=p.useRef(null),s=he(t,o),i=bP(n);return p.useEffect(()=>{const a=o.current,l=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==n&&d){const f=new Event("change",{bubbles:!0});d.call(a,n),a.dispatchEvent(f)}},[i,n]),u.jsx(Zs,{asChild:!0,children:u.jsx("select",{...r,ref:s,defaultValue:n})})});i0.displayName="BubbleSelect";function a0(e){const t=ct(e),n=p.useRef(""),r=p.useRef(0),o=p.useCallback(i=>{const a=n.current+i;t(a),function l(c){n.current=c,window.clearTimeout(r.current),c!==""&&(r.current=window.setTimeout(()=>l(""),1e3))}(a)},[t]),s=p.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return p.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,s]}function l0(e,t,n){const o=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=Rk(e,Math.max(s,0));o.length===1&&(i=i.filter(c=>c!==n));const l=i.find(c=>c.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function Rk(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var jk=Dy,u0=Ly,Ok=zy,Mk=$y,_k=Wy,c0=Uy,Ak=Qy,d0=Gy,f0=Xy,Dk=Zy,Ik=e0,h0=t0,p0=n0,m0=o0;const tp=jk,np=Ok,Ec=p.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(u0,{ref:r,className:X("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,u.jsx(Mk,{asChild:!0,children:u.jsx(Pv,{className:"h-4 w-4 opacity-50"})})]}));Ec.displayName=u0.displayName;const g0=p.forwardRef(({className:e,...t},n)=>u.jsx(h0,{ref:n,className:X("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(RC,{className:"h-4 w-4"})}));g0.displayName=h0.displayName;const v0=p.forwardRef(({className:e,...t},n)=>u.jsx(p0,{ref:n,className:X("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(Pv,{className:"h-4 w-4"})}));v0.displayName=p0.displayName;const bc=p.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>u.jsx(_k,{children:u.jsxs(c0,{ref:o,className:X("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[u.jsx(g0,{}),u.jsx(Ak,{className:X("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),u.jsx(v0,{})]})}));bc.displayName=c0.displayName;const Lk=p.forwardRef(({className:e,...t},n)=>u.jsx(d0,{ref:n,className:X("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));Lk.displayName=d0.displayName;const Ct=p.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(f0,{ref:r,className:X("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(Ik,{children:u.jsx(TC,{className:"h-4 w-4"})})}),u.jsx(Dk,{children:t})]}));Ct.displayName=f0.displayName;const Fk=p.forwardRef(({className:e,...t},n)=>u.jsx(m0,{ref:n,className:X("-mx-1 my-1 h-px bg-muted",e),...t}));Fk.displayName=m0.displayName;const y0=p.forwardRef(({className:e,type:t,...n},r)=>u.jsx("input",{type:t,className:X("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));y0.displayName="Input";var zk="Label",x0=p.forwardRef((e,t)=>u.jsx(J.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));x0.displayName=zk;var w0=x0;const $k=Js("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ma=p.forwardRef(({className:e,...t},n)=>u.jsx(w0,{ref:n,className:X($k(),e),...t}));Ma.displayName=w0.displayName;function Nc({onFiltersChange:e}){const[t,n]=p.useState("all"),[r,o]=p.useState("all"),[s,i]=p.useState(""),a=()=>{e({rating:t!=="all"?parseInt(t):void 0,sentiment:r!=="all"?r:void 0,search:s||void 0})},l=()=>{n("all"),o("all"),i(""),e({})};return u.jsx(Ne,{className:"mb-6",children:u.jsx(Pe,{className:"p-6",children:u.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-end sm:justify-between gap-4",children:[u.jsxs("div",{className:"flex flex-wrap gap-4",children:[u.jsxs("div",{className:"min-w-0",children:[u.jsx(Ma,{htmlFor:"rating-filter",className:"text-sm font-medium text-gray-700 mb-2 block",children:"Filter by Rating"}),u.jsxs(tp,{value:t,onValueChange:n,children:[u.jsx(Ec,{className:"w-[140px]",id:"rating-filter",children:u.jsx(np,{placeholder:"All Ratings"})}),u.jsxs(bc,{children:[u.jsx(Ct,{value:"all",children:"All Ratings"}),u.jsx(Ct,{value:"5",children:"5 Stars"}),u.jsx(Ct,{value:"4",children:"4 Stars"}),u.jsx(Ct,{value:"3",children:"3 Stars"}),u.jsx(Ct,{value:"2",children:"2 Stars"}),u.jsx(Ct,{value:"1",children:"1 Star"})]})]})]}),u.jsxs("div",{className:"min-w-0",children:[u.jsx(Ma,{htmlFor:"sentiment-filter",className:"text-sm font-medium text-gray-700 mb-2 block",children:"Sentiment"}),u.jsxs(tp,{value:r,onValueChange:o,children:[u.jsx(Ec,{className:"w-[140px]",id:"sentiment-filter",children:u.jsx(np,{placeholder:"All Sentiments"})}),u.jsxs(bc,{children:[u.jsx(Ct,{value:"all",children:"All Sentiments"}),u.jsx(Ct,{value:"positive",children:"Positive"}),u.jsx(Ct,{value:"neutral",children:"Neutral"}),u.jsx(Ct,{value:"negative",children:"Negative"})]})]})]}),u.jsxs("div",{className:"flex gap-2",children:[u.jsx(ze,{onClick:a,className:"bg-hotel-blue hover:bg-hotel-blue/90",children:"Apply Filters"}),u.jsx(ze,{onClick:l,variant:"outline",children:"Clear"})]})]}),u.jsx("div",{className:"flex items-center gap-2",children:u.jsxs("div",{className:"relative",children:[u.jsx(y0,{type:"search",placeholder:"Search reviews...",value:s,onChange:c=>i(c.target.value),onKeyDown:c=>c.key==="Enter"&&a(),className:"pl-10 w-64"}),u.jsx(AC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"})]})})]})})})}function Wk(e={}){const t=new URLSearchParams;return e.rating&&t.set("rating",e.rating.toString()),e.sentiment&&t.set("sentiment",e.sentiment),e.search&&t.set("search",e.search),e.limit&&t.set("limit",e.limit.toString()),e.offset&&t.set("offset",e.offset.toString()),t.toString(),jo({queryKey:["/api/reviews",e]})}const Uk=Js("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),fr=p.forwardRef(({className:e,variant:t,...n},r)=>u.jsx("div",{ref:r,role:"alert",className:X(Uk({variant:t}),e),...n}));fr.displayName="Alert";const Bk=p.forwardRef(({className:e,...t},n)=>u.jsx("h5",{ref:n,className:X("mb-1 font-medium leading-none tracking-tight",e),...t}));Bk.displayName="AlertTitle";const hr=p.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:X("text-sm [&_p]:leading-relaxed",e),...t}));hr.displayName="AlertDescription";var Vd="Dialog",[S0,y2]=Oo(Vd),[Vk,At]=S0(Vd),C0=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,a=p.useRef(null),l=p.useRef(null),[c=!1,d]=Ea({prop:r,defaultProp:o,onChange:s});return u.jsx(Vk,{scope:t,triggerRef:a,contentRef:l,contentId:Jr(),titleId:Jr(),descriptionId:Jr(),open:c,onOpenChange:d,onOpenToggle:p.useCallback(()=>d(f=>!f),[d]),modal:i,children:n})};C0.displayName=Vd;var E0="DialogTrigger",Hk=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(E0,n),s=he(t,o.triggerRef);return u.jsx(J.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Yd(o.open),...r,ref:s,onClick:Q(e.onClick,o.onOpenToggle)})});Hk.displayName=E0;var Hd="DialogPortal",[Qk,b0]=S0(Hd,{forceMount:void 0}),N0=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=At(Hd,t);return u.jsx(Qk,{scope:t,forceMount:n,children:p.Children.map(r,i=>u.jsx(Mo,{present:n||s.open,children:u.jsx(Za,{asChild:!0,container:o,children:i})}))})};N0.displayName=Hd;var _a="DialogOverlay",P0=p.forwardRef((e,t)=>{const n=b0(_a,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=At(_a,e.__scopeDialog);return s.modal?u.jsx(Mo,{present:r||s.open,children:u.jsx(Kk,{...o,ref:t})}):null});P0.displayName=_a;var Yk=bo("DialogOverlay.RemoveScroll"),Kk=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(_a,n);return u.jsx(Ud,{as:Yk,allowPinchZoom:!0,shards:[o.contentRef],children:u.jsx(J.div,{"data-state":Yd(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Cr="DialogContent",k0=p.forwardRef((e,t)=>{const n=b0(Cr,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=At(Cr,e.__scopeDialog);return u.jsx(Mo,{present:r||s.open,children:s.modal?u.jsx(Gk,{...o,ref:t}):u.jsx(qk,{...o,ref:t})})});k0.displayName=Cr;var Gk=p.forwardRef((e,t)=>{const n=At(Cr,e.__scopeDialog),r=p.useRef(null),o=he(t,n.contentRef,r);return p.useEffect(()=>{const s=r.current;if(s)return ky(s)},[]),u.jsx(T0,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Q(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:Q(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:Q(e.onFocusOutside,s=>s.preventDefault())})}),qk=p.forwardRef((e,t)=>{const n=At(Cr,e.__scopeDialog),r=p.useRef(!1),o=p.useRef(!1);return u.jsx(T0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var l,c;(l=e.onInteractOutside)==null||l.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;((c=n.triggerRef.current)==null?void 0:c.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),T0=p.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...i}=e,a=At(Cr,n),l=p.useRef(null),c=he(t,l);return by(),u.jsxs(u.Fragment,{children:[u.jsx(Wd,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:u.jsx(Xs,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Yd(a.open),...i,ref:c,onDismiss:()=>a.onOpenChange(!1)})}),u.jsxs(u.Fragment,{children:[u.jsx(Xk,{titleId:a.titleId}),u.jsx(Jk,{contentRef:l,descriptionId:a.descriptionId})]})]})}),Qd="DialogTitle",R0=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(Qd,n);return u.jsx(J.h2,{id:o.titleId,...r,ref:t})});R0.displayName=Qd;var j0="DialogDescription",O0=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(j0,n);return u.jsx(J.p,{id:o.descriptionId,...r,ref:t})});O0.displayName=j0;var M0="DialogClose",_0=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(M0,n);return u.jsx(J.button,{type:"button",...r,ref:t,onClick:Q(e.onClick,()=>o.onOpenChange(!1))})});_0.displayName=M0;function Yd(e){return e?"open":"closed"}var A0="DialogTitleWarning",[x2,D0]=DS(A0,{contentName:Cr,titleName:Qd,docsSlug:"dialog"}),Xk=({titleId:e})=>{const t=D0(A0),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return p.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Zk="DialogDescriptionWarning",Jk=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${D0(Zk).contentName}}.`;return p.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},e2=C0,t2=N0,I0=P0,L0=k0,F0=R0,z0=O0,n2=_0;const r2=e2,o2=t2,$0=p.forwardRef(({className:e,...t},n)=>u.jsx(I0,{ref:n,className:X("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));$0.displayName=I0.displayName;const W0=p.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(o2,{children:[u.jsx($0,{}),u.jsxs(L0,{ref:r,className:X("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,u.jsxs(n2,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[u.jsx(Rv,{className:"h-4 w-4"}),u.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));W0.displayName=L0.displayName;const U0=({className:e,...t})=>u.jsx("div",{className:X("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});U0.displayName="DialogHeader";const B0=p.forwardRef(({className:e,...t},n)=>u.jsx(F0,{ref:n,className:X("text-lg font-semibold leading-none tracking-tight",e),...t}));B0.displayName=F0.displayName;const V0=p.forwardRef(({className:e,...t},n)=>u.jsx(z0,{ref:n,className:X("text-sm text-muted-foreground",e),...t}));V0.displayName=z0.displayName;const H0=p.forwardRef(({className:e,...t},n)=>u.jsx("textarea",{className:X("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...t}));H0.displayName="Textarea";function s2({review:e,open:t,onOpenChange:n}){const[r,o]=p.useState(""),[s,i]=p.useState(!1),{toast:a}=Xa(),l=async()=>{if(!(!e||!r.trim())){i(!0);try{await Xr("POST",`/api/reviews/${e.id}/response`,{responseComment:r.trim()}),Ca.invalidateQueries({queryKey:["/api/reviews"]}),Ca.invalidateQueries({queryKey:["/api/reviews/stats"]}),a({title:"Response Posted",description:"Your response has been posted successfully."}),o(""),n(!1)}catch{a({title:"Error",description:"Failed to post response. Please try again.",variant:"destructive"})}finally{i(!1)}}},c=f=>Array.from({length:5},(h,x)=>u.jsx(As,{className:`h-4 w-4 ${x<f?"text-hotel-yellow fill-current":"text-gray-300"}`},x)),d=f=>{switch(f){case"positive":return"bg-green-100 text-green-800";case"negative":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}};return p.useEffect(()=>{t||o((e==null?void 0:e.responseComment)||"")},[t,e]),e?u.jsx(r2,{open:t,onOpenChange:n,children:u.jsxs(W0,{className:"max-w-2xl",children:[u.jsxs(U0,{children:[u.jsx(B0,{children:e.responseComment?"Update Response":"Respond to Review"}),u.jsx(V0,{children:"Craft a thoughtful response to this customer review."})]}),u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[u.jsx("div",{className:"flex items-center justify-between mb-3",children:u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("span",{className:"font-medium",children:e.reviewerName}),u.jsx("div",{className:"flex",children:c(e.rating)}),u.jsx(dr,{className:d(e.sentiment),children:e.sentiment})]})}),u.jsx("p",{className:"text-gray-700 text-sm leading-relaxed",children:e.comment})]}),e.responseComment&&u.jsxs("div",{className:"p-4 bg-blue-50 border-l-4 border-hotel-blue rounded-lg",children:[u.jsx("p",{className:"text-sm font-medium text-hotel-blue mb-2",children:"Current Response:"}),u.jsx("p",{className:"text-sm text-gray-700",children:e.responseComment})]}),u.jsxs("div",{className:"space-y-2",children:[u.jsx(Ma,{htmlFor:"response",children:e.responseComment?"Updated Response":"Your Response"}),u.jsx(H0,{id:"response",value:r,onChange:f=>o(f.target.value),placeholder:"Thank you for your feedback. We sincerely appreciate your review and would like to address your concerns...",rows:6,className:"resize-none"}),u.jsxs("p",{className:"text-xs text-gray-500",children:[r.length,"/1000 characters"]})]}),u.jsxs("div",{className:"flex justify-end space-x-3",children:[u.jsx(ze,{variant:"outline",onClick:()=>n(!1),disabled:s,children:"Cancel"}),u.jsx(ze,{onClick:l,disabled:s||!r.trim(),className:"bg-hotel-blue hover:bg-hotel-blue/90",children:s?"Posting...":e.responseComment?"Update Response":"Post Response"})]})]})]})}):null}function i2(){const[e,t]=p.useState({}),[n,r]=p.useState(null),[o,s]=p.useState(!1),{data:i,isLoading:a}=Wk(e),l=h=>{switch(h){case"positive":return"bg-green-100 text-green-800";case"negative":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}},c=h=>Array.from({length:5},(x,S)=>u.jsx(As,{className:`h-4 w-4 ${S<h?"text-hotel-yellow fill-current":"text-gray-300"}`},S)),d=h=>{r(h),s(!0)},f=h=>{navigator.share?navigator.share({title:`Review from ${h.reviewerName}`,text:h.comment,url:window.location.href}):navigator.clipboard.writeText(`Review from ${h.reviewerName}: ${h.comment}`)};return a?u.jsxs("div",{className:"space-y-6",children:[u.jsx(Nc,{onFiltersChange:t}),[...Array(3)].map((h,x)=>u.jsx(Ne,{className:"animate-pulse",children:u.jsx(Pe,{className:"p-6",children:u.jsx("div",{className:"h-32 bg-gray-200 rounded"})})},x))]}):u.jsxs("div",{className:"space-y-6",children:[u.jsx(Nc,{onFiltersChange:t}),i&&i.length===0?u.jsx(Ne,{children:u.jsx(Pe,{className:"p-6 text-center",children:u.jsx("p",{className:"text-gray-500",children:"No reviews found matching your criteria."})})}):u.jsx("div",{className:"space-y-6",children:i==null?void 0:i.map(h=>u.jsx(Ne,{children:u.jsxs(Pe,{className:"p-6",children:[u.jsxs("div",{className:"flex items-start justify-between mb-4",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"w-10 h-10 gradient-primary rounded-full flex items-center justify-center text-white font-semibold",children:u.jsx("span",{children:h.reviewerName.charAt(0).toUpperCase()})}),u.jsxs("div",{className:"ml-3",children:[u.jsx("h4",{className:"font-semibold text-gray-900",children:h.reviewerName}),u.jsx("p",{className:"text-sm text-gray-500",children:Gl(new Date(h.reviewDate),"MMMM d, yyyy")})]})]}),u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("div",{className:"flex mr-2",children:c(h.rating)}),u.jsxs("span",{className:"text-sm font-medium text-gray-700",children:[h.rating,".0"]})]}),u.jsx(dr,{className:l(h.sentiment),children:h.sentiment.charAt(0).toUpperCase()+h.sentiment.slice(1)})]})]}),u.jsx("div",{className:"mb-4",children:u.jsx("p",{className:"text-gray-700 leading-relaxed",children:h.comment})}),h.responseComment?u.jsx("div",{className:"bg-blue-50 border-l-4 border-hotel-blue p-4 mb-4",children:u.jsxs("div",{className:"flex items-start",children:[u.jsx(Bl,{className:"h-4 w-4 text-hotel-blue mt-1 mr-2"}),u.jsxs("div",{children:[u.jsx("p",{className:"text-sm font-medium text-hotel-blue",children:"Management Response"}),u.jsx("p",{className:"text-sm text-gray-700 mt-1",children:h.responseComment}),h.responseDate&&u.jsxs("p",{className:"text-xs text-gray-500 mt-2",children:["Responded on ",Gl(new Date(h.responseDate),"MMMM d, yyyy")]})]})]})}):h.sentiment==="negative"&&u.jsxs(fr,{className:"mb-4 border-red-200 bg-red-50",children:[u.jsx(Tv,{className:"h-4 w-4 text-red-500"}),u.jsx(hr,{className:"text-red-800",children:"This negative review requires management attention and response."})]}),u.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[u.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[u.jsx(_s,{className:"h-4 w-4 mr-1"}),u.jsxs("span",{children:["Synced ",Gl(new Date(h.fetchedAt),"MMM d, h:mm a")]})]}),u.jsxs("div",{className:"flex items-center gap-2",children:[h.responseComment?u.jsxs(ze,{onClick:()=>d(h),variant:"ghost",size:"sm",children:[u.jsx(Bl,{className:"h-4 w-4 mr-1"}),"Update Response"]}):u.jsxs(ze,{onClick:()=>d(h),className:h.sentiment==="negative"?"bg-hotel-blue hover:bg-hotel-blue/90":"",variant:h.sentiment==="negative"?"default":"ghost",children:[u.jsx(Bl,{className:"h-4 w-4 mr-1"}),h.sentiment==="negative"?"Respond Now":"Respond"]}),u.jsxs(ze,{onClick:()=>f(h),variant:"ghost",size:"sm",children:[u.jsx(DC,{className:"h-4 w-4 mr-1"}),"Share"]})]})]})]})},h.id))}),u.jsx(s2,{review:n,open:o,onOpenChange:s})]})}function a2(){const{data:e,isLoading:t}=jo({queryKey:["/api/quota-status"],refetchInterval:3e4});if(t)return u.jsx(Ne,{children:u.jsx(Pe,{className:"pt-6",children:u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"}),u.jsx("span",{className:"text-sm text-muted-foreground",children:"Checking API status..."})]})})});if(!e)return null;const n=()=>e.isConfigured?e.quota.exceeded?u.jsx(_s,{className:"h-4 w-4 text-orange-500"}):u.jsx(Or,{className:"h-4 w-4 text-green-500"}):u.jsx(Tv,{className:"h-4 w-4 text-yellow-500"}),r=()=>e.isConfigured?e.quota.exceeded?u.jsx(dr,{variant:"outline",className:"text-orange-600 border-orange-200",children:"Quota Exceeded"}):u.jsx(dr,{variant:"outline",className:"text-green-600 border-green-200",children:"Ready"}):u.jsx(dr,{variant:"secondary",children:"Not Configured"});return u.jsxs(Ne,{children:[u.jsx(Fs,{className:"pb-3",children:u.jsxs(zs,{className:"flex items-center justify-between text-base",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[n(),u.jsx("span",{children:"Google API Status"})]}),r()]})}),u.jsx(Pe,{className:"pt-0",children:u.jsxs("div",{className:"space-y-3",children:[u.jsx("div",{className:"text-sm text-muted-foreground",children:e.message}),e.quota.exceeded&&e.quota.retryAfter&&u.jsxs(fr,{children:[u.jsx(_s,{className:"h-4 w-4"}),u.jsxs(hr,{children:[u.jsx("strong",{children:"Quota Reset:"})," ",new Date(e.quota.retryAfter).toLocaleTimeString(),u.jsx("br",{}),u.jsx("span",{className:"text-xs text-muted-foreground mt-1 block",children:"Google Business Profile API has strict limits. This is normal behavior."})]})]}),e.isConfigured&&u.jsxs(fr,{children:[u.jsx(OC,{className:"h-4 w-4"}),u.jsxs(hr,{children:[u.jsx("strong",{children:"Your Reviews:"})," ",e.explanation,u.jsx("br",{}),u.jsx("span",{className:"text-xs text-muted-foreground mt-1 block",children:"The system automatically retries every 30 minutes when quota allows."})]})]}),e.lastSync&&u.jsxs("div",{className:"text-xs text-muted-foreground",children:["Last successful sync: ",new Date(e.lastSync).toLocaleString()]})]})})]})}function rp(){var c;const[,e]=Cd(),{data:t,isLoading:n}=vy(),[r,o]=p.useState(!1),{toast:s}=Xa(),i=async()=>{o(!0);try{const f=await(await Xr("POST","/api/refresh-business")).json();f.success?(s({title:"Business data refreshed",description:"Your Google Business Profile data has been updated."}),window.location.reload()):s({title:"Refresh failed",description:f.error||"Unable to refresh business data. Google API quota may still be limited.",variant:"destructive"})}catch{s({title:"Refresh failed",description:"Unable to refresh business data. Please try again later.",variant:"destructive"})}finally{o(!1)}},a=async()=>{try{const f=await(await Xr("GET","/api/test-quota")).json();f.quotaOk?s({title:"✅ Authentication Ready",description:f.message+(f.warning?` ⚠️ ${f.warning}`:"")}):s({title:"❌ Authentication Error",description:f.message||f.error,variant:"destructive"})}catch{s({title:"Test failed",description:"Unable to test authentication status.",variant:"destructive"})}},l=async()=>{try{const f=await(await Xr("GET","/api/quota-guidance")).json();f.connected?s({title:"📋 Quota Increase Required",description:`Google Business Profile API has very low quota limits. Go to Google Cloud Console > APIs & Services > Quotas to request increases for project ${f.projectNumber}. Quota resets at ${f.quotaResetTime}.`,duration:1e4}):s({title:"Not Connected",description:f.message||"Please connect your Google account first.",variant:"destructive"})}catch{s({title:"Error",description:"Unable to get quota guidance.",variant:"destructive"})}};return p.useEffect(()=>{!n&&t&&!t.isSetup&&e("/setup")},[t,n,e]),n?u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),u.jsx("p",{className:"text-muted-foreground",children:"Loading dashboard..."})]})}):t!=null&&t.isSetup?u.jsxs("div",{className:"min-h-screen bg-slate-50",children:[u.jsx(dP,{}),u.jsxs("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[u.jsxs("div",{className:"mb-8 flex justify-between items-start",children:[u.jsxs("div",{children:[u.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Review Dashboard"}),t.businessInfo&&u.jsxs(u.Fragment,{children:[u.jsx("p",{className:"mt-2 text-gray-600",children:t.businessInfo.name}),t.businessInfo.address&&u.jsx("p",{className:"text-sm text-gray-500",children:t.businessInfo.address})]})]}),((c=t==null?void 0:t.businessInfo)==null?void 0:c.name)==="Loading Business Information..."&&u.jsxs("div",{className:"flex gap-2",children:[u.jsxs(ze,{onClick:a,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[u.jsx(Vl,{className:"h-4 w-4"}),"Test Auth"]}),u.jsxs(ze,{onClick:l,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[u.jsx(Vl,{className:"h-4 w-4"}),"Quota Help"]}),u.jsxs(ze,{onClick:i,disabled:r,variant:"outline",className:"flex items-center gap-2",children:[u.jsx(kv,{className:`h-4 w-4 ${r?"animate-spin":""}`}),r?"Fetching...":"Fetch Business Data"]})]})]}),u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6",children:[u.jsx("div",{className:"lg:col-span-3",children:u.jsx(hP,{})}),u.jsx("div",{className:"lg:col-span-1",children:u.jsx(a2,{})})]}),u.jsx(pP,{}),u.jsx(Nc,{}),u.jsx(i2,{})]})]}):u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:u.jsx(Ne,{className:"w-full max-w-md mx-4",children:u.jsxs(Pe,{className:"pt-6 text-center",children:[u.jsx(ba,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),u.jsx("h1",{className:"text-xl font-semibold mb-2",children:"Setup Required"}),u.jsx("p",{className:"text-muted-foreground mb-4",children:"Please complete the setup to start managing your hotel reviews."}),u.jsxs(ze,{onClick:()=>e("/setup"),children:[u.jsx(Vl,{className:"h-4 w-4 mr-2"}),"Start Setup"]})]})})})}function l2(){const[,e]=Cd(),{data:t,isLoading:n}=Yb(),[r,o]=p.useState("pending"),[s,i]=p.useState("");p.useEffect(()=>{const l=new URLSearchParams(window.location.search),c=l.get("setup"),d=l.get("message");c==="complete"?(o("complete"),setTimeout(()=>{e("/")},3e3)):c==="error"&&(o("error"),i(d||"An unknown error occurred"))},[e]),p.useEffect(()=>{!n&&(t!=null&&t.isSetup)&&e("/")},[t,n,e]);const a=()=>{t!=null&&t.setupUrl&&(o("connecting"),window.location.href=t.setupUrl)};return n?u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),u.jsx("p",{className:"text-muted-foreground",children:"Loading setup..."})]})}):u.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4",children:u.jsxs("div",{className:"w-full max-w-2xl",children:[u.jsxs("div",{className:"text-center mb-8",children:[u.jsx("div",{className:"w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4",children:u.jsx(ba,{className:"h-8 w-8 text-white"})}),u.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Hotel Review Management"}),u.jsx("p",{className:"text-gray-600",children:"Connect your Google Business Profile to start managing reviews"})]}),r==="complete"?u.jsx(Ne,{children:u.jsxs(Pe,{className:"pt-6 text-center",children:[u.jsx(Or,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),u.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Setup Complete!"}),u.jsx("p",{className:"text-gray-600 mb-4",children:"Your Google Business Profile has been connected successfully. Redirecting to dashboard..."}),u.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"})]})}):r==="error"?u.jsx(Ne,{children:u.jsxs(Pe,{className:"pt-6",children:[u.jsxs("div",{className:"text-center mb-6",children:[u.jsx(Qi,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),u.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Setup Failed"}),u.jsx("p",{className:"text-gray-600 mb-4",children:s})]}),u.jsxs(fr,{className:"mb-6",children:[u.jsx(Qi,{className:"h-4 w-4"}),u.jsx(hr,{children:"This usually happens when the authorization code expires. Please try again."})]}),u.jsxs("div",{className:"space-y-4",children:[u.jsx(ze,{onClick:()=>{o("pending"),i(""),window.history.replaceState({},"",window.location.pathname)},className:"w-full bg-hotel-blue hover:bg-hotel-blue/90",size:"lg",children:"Try Again"}),u.jsxs("div",{className:"text-sm text-gray-600",children:[u.jsx("p",{className:"font-medium mb-2",children:"Common solutions:"}),u.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[u.jsx("li",{children:"Make sure you have a Google Business Profile"}),u.jsx("li",{children:"Complete the authorization quickly after clicking"}),u.jsx("li",{children:"Check that you granted all requested permissions"})]})]})]})]})}):u.jsxs(Ne,{children:[u.jsxs(Fs,{children:[u.jsxs(zs,{className:"flex items-center gap-2",children:[u.jsx(ba,{className:"h-5 w-5"}),"Connect Your Business"]}),u.jsx(Ey,{children:"Authorize access to automatically fetch and manage your Google Business Profile reviews."})]}),u.jsxs(Pe,{className:"space-y-6",children:[r==="connecting"&&u.jsxs(fr,{children:[u.jsx(_s,{className:"h-4 w-4"}),u.jsx(hr,{children:"Connecting to Google Business Profile... Please complete the authorization in the popup window."})]}),u.jsxs("div",{className:"space-y-3",children:[u.jsxs("div",{className:"flex items-center gap-3 p-3 bg-green-50 rounded-lg",children:[u.jsx(Or,{className:"h-5 w-5 text-green-600"}),u.jsx("span",{className:"text-sm text-gray-700",children:"Automatic review synchronization"})]}),u.jsxs("div",{className:"flex items-center gap-3 p-3 bg-green-50 rounded-lg",children:[u.jsx(Or,{className:"h-5 w-5 text-green-600"}),u.jsx("span",{className:"text-sm text-gray-700",children:"Real-time review analytics"})]}),u.jsxs("div",{className:"flex items-center gap-3 p-3 bg-green-50 rounded-lg",children:[u.jsx(Or,{className:"h-5 w-5 text-green-600"}),u.jsx("span",{className:"text-sm text-gray-700",children:"Response management tools"})]}),u.jsxs("div",{className:"flex items-center gap-3 p-3 bg-green-50 rounded-lg",children:[u.jsx(Or,{className:"h-5 w-5 text-green-600"}),u.jsx("span",{className:"text-sm text-gray-700",children:"Sentiment analysis"})]})]}),u.jsxs("div",{className:"border-t pt-6",children:[u.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[u.jsx("h3",{className:"font-medium text-gray-900",children:"Required Permissions"}),u.jsx(dr,{variant:"secondary",children:"Secure"})]}),u.jsxs("div",{className:"text-sm text-gray-600 space-y-1",children:[u.jsx("p",{children:"• Read your business profile information"}),u.jsx("p",{children:"• Access customer reviews"}),u.jsx("p",{children:"• Post responses to reviews"})]})]}),u.jsxs(ze,{onClick:a,disabled:r==="connecting"||!(t!=null&&t.setupUrl),className:"w-full bg-hotel-blue hover:bg-hotel-blue/90",size:"lg",children:[u.jsx(jC,{className:"h-4 w-4 mr-2"}),r==="connecting"?"Connecting...":"Authorize Google Business Profile"]}),u.jsxs(fr,{children:[u.jsx(Qi,{className:"h-4 w-4"}),u.jsx(hr,{children:"This app requires access to your Google Business Profile to fetch reviews. Your data is never shared with third parties."})]})]})]})]})})}function u2(){return u.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:u.jsx(Ne,{className:"w-full max-w-md mx-4",children:u.jsxs(Pe,{className:"pt-6",children:[u.jsxs("div",{className:"flex mb-4 gap-2",children:[u.jsx(Qi,{className:"h-8 w-8 text-red-500"}),u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),u.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function c2(){return u.jsxs(G1,{children:[u.jsx(Ci,{path:"/",component:rp}),u.jsx(Ci,{path:"/dashboard",component:rp}),u.jsx(Ci,{path:"/setup",component:l2}),u.jsx(Ci,{component:u2})]})}function d2(){return u.jsx(gS,{client:Ca,children:u.jsxs(Hb,{children:[u.jsx(yE,{}),u.jsx(c2,{})]})})}kg(document.getElementById("root")).render(u.jsx(d2,{}));
